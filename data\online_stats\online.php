<?php
// online.php 增强版（带历史访问统计）
$timeout = 300;
$logFile = __DIR__ . '/.online_log';

try {
    // 检测必要参数是否存在
    $isWebRequest = isset($_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
    
    // 读取现有数据
    $data = file_exists($logFile) 
        ? json_decode(file_get_contents($logFile), true) 
        : ['active' => [], 'total' => 0];
    
    // 初始化数据结构（兼容旧版本）
    if (!isset($data['total'])) {
        $data['total'] = count($data);
        $data['active'] = $data;
    }
    
    // 清理过期记录
    $currentTime = time();
    foreach($data['active'] as $key => $timestamp) {
        if($currentTime - $timestamp > $timeout) {
            unset($data['active'][$key]);
        }
    }
    
    // 仅在Web请求时更新记录
    if($isWebRequest) {
        $Hash = hash('sha256', $_SERVER['REMOTE_ADDR'] . $_SERVER['HTTP_USER_AGENT']);
        
        // 如果是新会话则增加总计数
        if (!isset($data['active'][$Hash])) {
            $data['total']++;
        }
        
        $data['active'][$Hash] = $currentTime;
    }
    
    // 保存数据（原子操作）
    file_put_contents($logFile, json_encode($data), LOCK_EX);
    
    // 返回在线人数和历史总数（按需选择输出）
    echo json_encode([
    'online' => count($data['active']),
    'total' => $data['total']
]);
    
} catch (Exception $e) {
    error_log("Online Counter Error: " . $e->getMessage());
    echo "0";
}
?>
