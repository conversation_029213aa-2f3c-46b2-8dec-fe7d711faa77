<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 自动评论功能模块
 * 当网站发布时自动添加随机评论
 */

/**
 * 预设评论内容库
 */
function get_default_comment_templates() {
    return array(
        '网站设计简洁大方，内容丰富且更新及时，用户体验非常棒，强烈推荐！',
        '界面美观，功能强大，信息准确，是我日常学习和工作的得力助手。',
        '网站加载速度快，资源全面，使用起来非常顺畅，点赞！',
        '内容专业性强，分类清晰，完全满足了我的需求，很赞！',
        '设计贴心，操作便捷，信息准确无误，真是一个优质的网站！',
        '资源丰富，内容实用，是我工作中不可或缺的工具！',
        '网站布局合理，功能齐全，完全超出我的预期，太好了！',
        '信息全面，更新及时，帮助我快速解决了问题，非常实用！',
        '用户界面友好，操作简单，完全适合新手使用！',
        '网站内容质量高，分类明确，查找信息非常方便！',
        '资源更新速度快，专业性强，是我学习的首选网站！',
        '界面清爽，设计感十足，使用体验非常舒适！',
        '功能多样且实用，内容涵盖范围广，太棒了！',
        '网站加载速度很快，资源丰富，完全满足我的需求！',
        '内容详细且有深度，分类清晰，非常适合研究使用！',
        '设计简约而不失美感，功能强大，用户体验极佳！',
        '网站信息准确，更新及时，帮助我节省了很多时间！',
        '界面美观，操作流畅，完全是一个高质量的网站！',
        '资源丰富，功能齐全，是我工作中常用的工具之一！',
        '网站内容专业且全面，设计贴心，非常值得推荐！',
        '页面响应速度快，内容组织有序，使用起来很舒心！',
        '功能实用，界面简洁，完全符合我的使用习惯！',
        '内容权威可靠，更新频率高，是个值得信赖的网站！',
        '设计现代化，功能完善，给我留下了深刻的印象！',
        '网站服务周到，内容丰富，真正做到了用户至上！'
    );
}

/**
 * 获取随机评论内容
 * @param int $count 需要的评论数量，默认5条
 * @return array 随机选择的评论内容数组
 */
function get_random_comment_contents($count = 5) {
    global $DB;
    
    // 首先尝试从数据库获取自定义评论模板
    $custom_templates = array();
    $table_name = $DB->table('auto_comment_templates');
    
    // 检查表是否存在
    $table_exists = $DB->query("SHOW TABLES LIKE '$table_name'");
    if ($DB->num_rows($table_exists)) {
        $query = $DB->query("SELECT content FROM $table_name WHERE status = 1 ORDER BY RAND()");
        while ($row = $DB->fetch_array($query)) {
            $custom_templates[] = $row['content'];
        }
    }
    
    // 如果没有自定义模板，使用默认模板
    $templates = !empty($custom_templates) ? $custom_templates : get_default_comment_templates();
    
    // 随机选择指定数量的评论
    $selected = array();
    $template_count = count($templates);
    $actual_count = min($count, $template_count);
    
    // 随机选择不重复的评论
    $selected_indexes = array_rand($templates, $actual_count);
    if (!is_array($selected_indexes)) {
        $selected_indexes = array($selected_indexes);
    }
    
    foreach ($selected_indexes as $index) {
        $selected[] = $templates[$index];
    }
    
    return $selected;
}

/**
 * 获取随机用户作为评论者
 * @param int $count 需要的用户数量
 * @return array 用户信息数组
 */
function get_random_comment_users($count = 5) {
    global $DB;
    
    $users = array();
    
    // 获取活跃会员（排除管理员）
    $user_table = $DB->table('users');
    $query = $DB->query("SELECT user_id, user_email, nick_name FROM $user_table WHERE user_type != 'admin' AND user_status = 1 ORDER BY RAND() LIMIT $count");
    
    while ($row = $DB->fetch_array($query)) {
        $users[] = array(
            'user_id' => $row['user_id'],
            'user_email' => $row['user_email'],
            'user_name' => $row['nick_name'] ?: '会员用户',
            'is_anonymous' => false
        );
    }
    
    // 如果会员不足，补充匿名用户
    $anonymous_names = array('热心网友', '匿名用户', '网站访客', '路过用户', '体验用户');
    while (count($users) < $count) {
        $users[] = array(
            'user_id' => 0,
            'user_email' => '',
            'user_name' => $anonymous_names[array_rand($anonymous_names)],
            'is_anonymous' => true
        );
    }
    
    return $users;
}

/**
 * 生成随机评分
 * @return array 包含三个维度评分的数组
 */
function generate_random_ratings() {
    // 生成4-5星的高评分，偶尔3星
    $ratings = array(3, 4, 4, 4, 5, 5, 5);
    
    return array(
        'content_quality' => $ratings[array_rand($ratings)],
        'service_quality' => $ratings[array_rand($ratings)],
        'trust_level' => $ratings[array_rand($ratings)]
    );
}

/**
 * 为网站自动添加评论
 * @param int $web_id 网站ID
 * @param int $comment_count 评论数量，默认5条
 * @return array 操作结果
 */
function auto_add_website_comments($web_id, $comment_count = 5) {
    global $DB, $options;
    
    // 验证参数
    $web_id = intval($web_id);
    if ($web_id <= 0) {
        return array('success' => false, 'message' => '无效的网站ID');
    }
    
    // 检查网站是否存在且已发布
    $website_table = $DB->table('websites');
    $website = $DB->fetch_one("SELECT web_id, web_name, web_status FROM $website_table WHERE web_id = $web_id");
    if (!$website) {
        return array('success' => false, 'message' => '网站不存在');
    }
    
    if ($website['web_status'] != 3) {
        return array('success' => false, 'message' => '网站未发布，无法添加评论');
    }
    
    // 检查是否已经有自动评论（避免重复添加）
    $comment_table = $DB->table('website_comments');
    $existing_count = $DB->get_count($comment_table, "web_id = $web_id AND comment_content LIKE '%网站设计简洁大方%'");
    if ($existing_count > 0) {
        return array('success' => false, 'message' => '该网站已有自动评论，避免重复添加');
    }
    
    // 获取随机评论内容和用户
    $comment_contents = get_random_comment_contents($comment_count);
    $comment_users = get_random_comment_users($comment_count);
    
    $success_count = 0;
    $current_time = time();
    
    // 为每条评论添加随机时间间隔（1-30分钟）
    $time_offset = 0;
    
    for ($i = 0; $i < $comment_count && $i < count($comment_contents); $i++) {
        $content = $comment_contents[$i];
        $user = $comment_users[$i];
        $ratings = generate_random_ratings();
        
        // 随机时间间隔
        $time_offset += rand(60, 1800); // 1-30分钟
        $comment_time = $current_time + $time_offset;
        
        // 准备评论数据
        $comment_data = array(
            'web_id' => $web_id,
            'user_id' => $user['user_id'],
            'user_email' => $user['user_email'],
            'user_name' => $user['user_name'],
            'user_ip' => '127.0.0.1', // 系统自动评论使用本地IP
            'content_quality' => $ratings['content_quality'],
            'service_quality' => $ratings['service_quality'],
            'trust_level' => $ratings['trust_level'],
            'comment_content' => $content,
            'parent_id' => 0,
            'status' => 1,
            'create_time' => $comment_time
        );
        
        // 插入评论
        try {
            $result = $DB->insert($comment_table, $comment_data);
            if ($result) {
                $success_count++;
            }
        } catch (Exception $e) {
            error_log("自动评论插入失败: " . $e->getMessage());
        }
    }
    
    return array(
        'success' => true, 
        'message' => "成功为网站 {$website['web_name']} 添加了 {$success_count} 条自动评论",
        'added_count' => $success_count
    );
}

/**
 * 检查自动评论功能是否启用
 * @return bool
 */
function is_auto_comment_enabled() {
    global $options;
    return isset($options['auto_comment_enabled']) && $options['auto_comment_enabled'] == 'yes';
}

/**
 * 获取自动评论配置
 * @return array
 */
function get_auto_comment_config() {
    global $options;
    
    return array(
        'enabled' => is_auto_comment_enabled(),
        'comment_count' => isset($options['auto_comment_count']) ? intval($options['auto_comment_count']) : 5,
        'delay_enabled' => isset($options['auto_comment_delay']) && $options['auto_comment_delay'] == 'yes',
        'min_delay' => isset($options['auto_comment_min_delay']) ? intval($options['auto_comment_min_delay']) : 1,
        'max_delay' => isset($options['auto_comment_max_delay']) ? intval($options['auto_comment_max_delay']) : 30
    );
}
?>
