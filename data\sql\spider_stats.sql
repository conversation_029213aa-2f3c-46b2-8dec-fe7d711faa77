-- 爬虫访问统计表
CREATE TABLE IF NOT EXISTS `dir_spider_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL,
  `google_count` int(10) unsigned NOT NULL DEFAULT '0',
  `baidu_count` int(10) unsigned NOT NULL DEFAULT '0',
  `bing_count` int(10) unsigned NOT NULL DEFAULT '0',
  `yandex_count` int(10) unsigned NOT NULL DEFAULT '0',
  `sogou_count` int(10) unsigned NOT NULL DEFAULT '0',
  `so360_count` int(10) unsigned NOT NULL DEFAULT '0',
  `bytedance_count` int(10) unsigned NOT NULL DEFAULT '0',
  `yahoo_count` int(10) unsigned NOT NULL DEFAULT '0',
  `other_count` int(10) unsigned NOT NULL DEFAULT '0',
  `total_visits` int(10) unsigned NOT NULL DEFAULT '0',
  `total_sites` int(10) unsigned NOT NULL DEFAULT '0',
  `total_articles` int(10) unsigned NOT NULL DEFAULT '0',
  `total_outlinks` int(10) unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_date` (`stat_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 插入一些示例数据（基于您提供的数据）
INSERT INTO `dir_spider_stats` (`stat_date`, `google_count`, `baidu_count`, `bing_count`, `yandex_count`, `sogou_count`, `so360_count`, `bytedance_count`, `yahoo_count`, `total_visits`, `total_sites`, `total_articles`, `total_outlinks`) VALUES
('2025-07-12', 4222, 0, 1364, 0, 0, 7, 2980, 0, 10613, 2002, 4316, 163),
('2025-07-11', 2198, 0, 1686, 0, 0, 1, 3344, 0, 10500, 1980, 4280, 155),
('2025-07-10', 4427, 0, 1716, 0, 0, 1, 3341, 0, 10800, 2100, 4400, 170),
('2025-07-09', 3527, 1, 1784, 0, 0, 1, 3198, 0, 10200, 1950, 4200, 145),
('2025-07-08', 3464, 0, 1715, 0, 0, 0, 3385, 0, 9900, 1900, 4100, 140),
('2025-07-07', 2625, 1, 1202, 0, 0, 0, 3408, 0, 9500, 1850, 4000, 135),
('2025-07-06', 3140, 0, 1229, 0, 0, 0, 3214, 0, 9200, 1800, 3900, 130),
('2025-07-05', 4596, 1, 1203, 0, 1, 2, 3280, 0, 9800, 1950, 4050, 160),
('2025-07-04', 4999, 2, 1938, 0, 0, 0, 3339, 0, 11000, 2200, 4500, 180),
('2025-07-03', 4395, 0, 5079, 0, 0, 11, 3268, 0, 12500, 2500, 5000, 200),
('2025-07-02', 3206, 0, 6150, 0, 0, 0, 3486, 0, 12000, 2400, 4800, 190),
('2025-07-01', 5452, 0, 3724, 0, 0, 0, 3409, 0, 11800, 2350, 4700, 185),
('2025-06-30', 3058, 2, 1628, 0, 2, 0, 3313, 0, 9500, 1900, 3800, 150),
('2025-06-29', 5719, 2, 2074, 0, 0, 2, 3446, 0, 12000, 2400, 4800, 195),
('2025-06-28', 17340, 2, 2437, 0, 0, 0, 3411, 1, 23000, 4600, 9200, 460),
('2025-06-27', 8610, 2, 2311, 0, 0, 0, 3273, 1, 15000, 3000, 6000, 300),
('2025-06-26', 1734, 2, 2456, 0, 0, 2, 3298, 0, 8500, 1700, 3400, 170),
('2025-06-25', 2114, 0, 2789, 0, 1, 0, 1457, 0, 7200, 1440, 2880, 144),
('2025-06-24', 4854, 3, 3847, 0, 0, 0, 2680, 1, 12000, 2400, 4800, 240),
('2025-06-23', 4165, 0, 2281, 1, 0, 0, 3434, 0, 10500, 2100, 4200, 210),
('2025-06-22', 1599, 5, 2303, 0, 0, 1, 3339, 0, 8000, 1600, 3200, 160),
('2025-06-21', 2290, 1, 2162, 0, 0, 0, 3274, 0, 8500, 1700, 3400, 170),
('2025-06-20', 2593, 8, 2327, 0, 0, 4, 3472, 0, 9200, 1840, 3680, 184),
('2025-06-19', 2065, 0, 2558, 0, 0, 0, 3491, 1, 8800, 1760, 3520, 176),
('2025-06-18', 1864, 0, 2688, 0, 0, 0, 3406, 1, 8500, 1700, 3400, 170),
('2025-06-17', 3036, 1, 3136, 0, 1, 0, 3527, 0, 10500, 2100, 4200, 210),
('2025-06-16', 9152, 1, 3710, 0, 0, 3, 3414, 1, 17000, 3400, 6800, 340),
('2025-06-15', 8459, 0, 3490, 0, 0, 0, 3408, 0, 16000, 3200, 6400, 320),
('2025-06-14', 8536, 2, 3698, 0, 0, 0, 3309, 0, 16500, 3300, 6600, 330),
('2025-06-13', 5851, 1, 3633, 0, 1, 0, 3281, 0, 13500, 2700, 5400, 270),
('2025-06-12', 2562, 0, 3262, 0, 16, 1, 2802, 0, 9500, 1900, 3800, 190),
('2025-06-11', 1174, 0, 3315, 0, 0, 0, 3030, 0, 8200, 1640, 3280, 164),
('2025-06-10', 1380, 80, 2943, 0, 0, 0, 3447, 0, 8500, 1700, 3400, 170),
('2025-06-09', 1327, 2, 3076, 0, 0, 1, 2616, 0, 7800, 1560, 3120, 156),
('2025-06-08', 976, 3, 3006, 0, 0, 0, 3408, 0, 8000, 1600, 3200, 160),
('2025-06-07', 780, 0, 2539, 0, 1, 0, 3451, 0, 7500, 1500, 3000, 150),
('2025-06-06', 1134, 1, 2644, 0, 0, 2, 3349, 0, 7800, 1560, 3120, 156),
('2025-06-05', 1516, 0, 2583, 0, 0, 0, 3525, 0, 8200, 1640, 3280, 164),
('2025-06-04', 1479, 0, 2830, 0, 1, 0, 3437, 0, 8300, 1660, 3320, 166),
('2025-06-03', 1988, 2, 3132, 0, 0, 0, 3275, 0, 9000, 1800, 3600, 180),
('2025-06-02', 3231, 1, 3292, 0, 0, 1, 3552, 0, 10800, 2160, 4320, 216),
('2025-06-01', 2855, 0, 3188, 0, 0, 0, 3493, 0, 10200, 2040, 4080, 204);
