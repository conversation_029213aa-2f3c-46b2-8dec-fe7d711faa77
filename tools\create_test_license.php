<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 为test.95dir.com创建专用授权文件
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

// 为test.95dir.com创建授权信息
$license_info = array(
    'domain' => 'test.95dir.com',
    'expire_time' => time() + (365 * 24 * 60 * 60), // 1年后到期
    'version' => '95DIR-v3.0', // 精确版本匹配
    'license_type' => 'developer',
    'customer_name' => '测试用户',
    'customer_email' => '<EMAIL>',
    'machine_code' => '7e217d6d470d761543b548bca7549699', // 绑定机器码
    'generate_time' => time(),
    'generator' => 'TestLicenseCreator v1.0'
);

// 生成签名
$license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';

// 计算签名
$sign_data = $license_info;
ksort($sign_data);
$sign_string = '';
foreach ($sign_data as $key => $value) {
    $sign_string .= $key . '=' . $value . '&';
}
$sign_string = rtrim($sign_string, '&');
$license_info['signature'] = md5($sign_string . $license_key);

// 转换为JSON并加密
$json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);
$encrypted_data = authcode($json_data, 'ENCODE', $license_key);

// 保存授权文件
$license_file = ROOT_PATH . 'data/license.dat';

// 确保目录存在
$data_dir = ROOT_PATH . 'data/';
if (!is_dir($data_dir)) {
    mkdir($data_dir, 0755, true);
}

// 保存文件
$result = file_put_contents($license_file, $encrypted_data);

// 同时创建一个可下载的副本
$download_file = ROOT_PATH . 'data/license_for_test_95dir_com.dat';
file_put_contents($download_file, $encrypted_data);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>为test.95dir.com创建授权文件</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .result { padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-download { background: #17a2b8; }
        .btn-download:hover { background: #138496; }
        .steps { background: #e7f3ff; padding: 20px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">为test.95dir.com创建授权</div>
            <p>专门为您的测试站创建授权文件</p>
        </div>
        
        <?php if ($result !== false): ?>
        <div class="result success">
            <h3>✅ 授权文件创建成功！</h3>
            <p>已为 <strong>test.95dir.com</strong> 创建专用授权文件</p>
            <p>文件大小：<?php echo strlen($encrypted_data); ?> 字节</p>
        </div>
        <?php else: ?>
        <div class="result error">
            <h3>✗ 授权文件创建失败！</h3>
            <p>无法写入授权文件，请检查目录权限。</p>
        </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>授权信息</h3>
            <div class="info-item">
                <span class="info-label">授权域名：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['domain']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">机器码：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['machine_code']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">到期时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['expire_time']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权版本：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['version']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权类型：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['license_type']); ?></span>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="?download=license_for_test_95dir_com.dat" class="btn btn-download">下载授权文件</a>
            <a href="../index.php" class="btn btn-success">测试网站首页</a>
        </div>
        
        <div class="steps">
            <h4>📋 部署步骤：</h4>
            <ol>
                <li><strong>下载授权文件</strong>：点击上面的"下载授权文件"按钮</li>
                <li><strong>重命名文件</strong>：将下载的文件重命名为 <code>license.dat</code></li>
                <li><strong>上传到测试站</strong>：上传到 <code>test.95dir.com/data/license.dat</code></li>
                <li><strong>验证授权</strong>：访问 <code>https://test.95dir.com</code> 检查是否正常</li>
            </ol>
            
            <p><strong>注意：</strong>文件名必须是 <code>license.dat</code>，不能有其他后缀！</p>
        </div>
    </div>
</body>
</html>

<?php
// 处理下载
if (isset($_GET['download'])) {
    $download_file = ROOT_PATH . 'data/' . basename($_GET['download']);
    if (file_exists($download_file)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="license.dat"');
        header('Content-Length: ' . filesize($download_file));
        readfile($download_file);
        exit;
    }
}
?>
