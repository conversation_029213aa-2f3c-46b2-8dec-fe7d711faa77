<?php
/* Smarty version 4.5.5, created on 2025-07-27 01:25:53
  from '/www/wwwroot/www.95dir.com/themes/member/article.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_68850fa1b49d74_75796361',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'e8e9b5796b153911f4daadbdbe0deea7869a986f' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/member/article.html',
      1 => 1739588647,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_68850fa1b49d74_75796361 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
		
            <div class="content">
            	<div class="title"><?php echo $_smarty_tpl->tpl_vars['pagename']->value;?>
</div>
                <div class="body">
        			<?php if ($_smarty_tpl->tpl_vars['action']->value == 'list') {?>
        			<div id="listbox">
						<table width="100%" border="0" cellspacing="1" cellpadding="0">
							<tr>
								<th>ID</th>
								<th>所属分类</th>
								<th>文章标题</th>
								<th>属性状态</th>
								<th>发布时间</th>
								<th>操作选项</th>
							</tr>
							<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['articles']->value, 'row');
$_smarty_tpl->tpl_vars['row']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['row']->value) {
$_smarty_tpl->tpl_vars['row']->do_else = false;
?>
							<tr>
								<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
</td>
								<td><?php echo $_smarty_tpl->tpl_vars['row']->value['cate_name'];?>
</td>
								<td class="textleft"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_title'];?>
</td>
								<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_attr'];?>
</td>
								<td><?php echo $_smarty_tpl->tpl_vars['row']->value['art_ctime'];?>
</td>
								<td><a href="?mod=article&act=edit&aid=<?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
">修改</a></td>
							</tr>
							<?php
}
if ($_smarty_tpl->tpl_vars['row']->do_else) {
?>
							<tr><td colspan="6">您还未发布任何文章！</td></tr>
							<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
						</table>
					</div>
        			<div id="showpage" class="clearfix"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
        			<?php }?>
        
        			<?php if ($_smarty_tpl->tpl_vars['action']->value == 'add' || $_smarty_tpl->tpl_vars['action']->value == 'edit') {?>
        			<div id="formbox">
						<form name="myfrom" id="myfrom" method="post" action="?mod=article">
        				<ul>
        					<li><strong>选择分类：</strong><select name="cate_id"><?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>
</select></li>
        					<li><strong>文章标题：</strong><input name="art_title" type="text" class="ipt" id="art_title" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_title'];?>
" /></li>
            				<li><strong>TAG标签：</strong><input name="art_tags" type="text" class="ipt" id="art_tags" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_tags'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /></li>
            				<li><strong>内容来源：</strong><input name="copy_from" type="text" class="ipt" id="copy_from" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['copy_from'];?>
" /></li>
							<li><strong>来源地址：</strong><input name="copy_url" type="text" class="ipt" id="copy_url" size="50" maxlength="200" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['copy_url'];?>
" /></li>
							<li><strong>内容摘要：</strong><textarea name="art_intro" cols="50" rows="5" class="ipt" id="art_intro"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_intro'];?>
</textarea></li>
                			<li><strong>文章内容：</strong>
							<?php echo '<script'; ?>
 type="text/javascript">
							var editor;
							KindEditor.ready(function(K) {
							editor = K.create('textarea[name="art_content"]', {
								resizeType : 1,
								allowPreviewEmoticons : false,
								allowImageUpload : true,
								uploadJson : '?mod=upload&act=upload',
								items : [
									'source', '|', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',
									'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',
									'insertunorderedlist', '|', 'emoticons', 'image', 'link', '|', 'fullscreen']
								});
							});
                    		<?php echo '</script'; ?>
>
                    		<textarea name="art_content" id="art_content" cols="50" rows="6" class="ipt" style="width: 530px; height: 400px; visibility: hidden;"><?php echo $_smarty_tpl->tpl_vars['row']->value['art_content'];?>
</textarea></li>
            				<li><strong>&nbsp;</strong><input type="hidden" name="art_id" id="art_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['art_id'];?>
"><input type="hidden" name="do" id="do" value="<?php echo $_smarty_tpl->tpl_vars['do']->value;?>
"><input type="submit" class="btn" value="提 交"> <input type="reset" class="btn" value="重 填"></li>
        				</ul>
        				</form>
    				</div>
					<?php }?>
            	</div>
			</div>
            
<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
