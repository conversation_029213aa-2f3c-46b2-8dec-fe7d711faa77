<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = 'VIP网站详情';
$pageurl = '?mod=vip_detail';
$tempfile = 'vip_detail.html';

$web_id = intval($_GET['id']);
$cache_id = $web_id;

if (!$web_id) {
    redirect('?mod=webdir');
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 查询VIP网站，只显示已审核且付费的网站
	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_tags, w.web_ctime, w.cate_id, w.user_id, w.web_pic,
	               w.web_ispay, w.web_istop, w.web_isbest, w.web_ai_intro,
	               d.web_utime, d.web_views, d.web_ip, d.web_grank, d.web_brank, d.web_srank, d.web_arank, 
	               d.web_instat, d.web_outstat
	        FROM " . $DB->table('websites') . " w
	        LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id = d.web_id
	        WHERE w.web_status = 3 AND w.web_ispay = 1 AND w.web_id = $web_id
	        LIMIT 1";

	$web = $DB->fetch_one($sql);
	if (!$web) {
		redirect('?mod=webdir');
	}

	// 增加浏览量
	$DB->query("UPDATE " . $DB->table('webdata') . " SET web_views=web_views+1 WHERE web_id=" . $web['web_id'] . " LIMIT 1");

	$cate = get_one_category($web['cate_id']);
	$user = get_one_user($web['user_id']);

	// 格式化数据
	$web['web_furl'] = format_url($web['web_url']);
	$web['web_pic'] = get_webthumb($web['web_pic']);
	$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
	$web['web_utime'] = date('Y-m-d', $web['web_utime']);
	$web['web_links'] = '/go.php?url=http://' . $web['web_url'];
	$web['web_link'] = '?mod=vip_detail&id=' . $web['web_id'];

	// VIP特殊标识
	$web['is_vip'] = true;
	$web['vip_level'] = $web['web_istop'] ? 'SVIP' : 'VIP';
	$web['vip_badge_color'] = $web['web_istop'] ? '#ff6b35' : '#ffd700';

	// 优化VIP详情页SEO
	$seo_title = $web['web_name'] . ' - ' . ($web['web_istop'] ? 'SVIP' : 'VIP') . '认证网站 - ' . $cate['cate_name'] . ' - ' . $options['site_name'];
	$smarty->assign('site_title', $seo_title);

	// 优化关键词
	$seo_keywords = array();
	if (!empty($web['web_tags'])) {
		$tags_array = explode(',', $web['web_tags']);
		$seo_keywords = array_merge($seo_keywords, $tags_array);
	}
	$seo_keywords[] = $web['web_name'];
	$seo_keywords[] = $cate['cate_name'];
	$seo_keywords[] = 'VIP网站';
	$seo_keywords[] = '优质网站';
	$seo_keywords[] = '认证网站';
	if ($web['web_istop']) {
		$seo_keywords[] = 'SVIP网站';
		$seo_keywords[] = '顶级网站';
	}
	if (!empty($options['site_keywords'])) {
		$site_keywords = explode(',', $options['site_keywords']);
		$seo_keywords = array_merge($seo_keywords, $site_keywords);
	}
	$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));

	// 优化描述
	$seo_description = '';
	if (!empty($web['web_intro'])) {
		$seo_description = strip_tags($web['web_intro']);
	} else if (!empty($web['web_ai_intro'])) {
		$seo_description = strip_tags($web['web_ai_intro']);
	} else {
		$seo_description = $web['web_name'] . '是一个优质的' . $cate['cate_name'] . '网站';
	}

	// 添加VIP信息到描述
	$vip_level = $web['web_istop'] ? 'SVIP' : 'VIP';
	$seo_description .= '，已通过' . $vip_level . '认证';
	if (isset($web['web_views']) && $web['web_views'] > 0) {
		$seo_description .= '，访问量' . $web['web_views'];
	}
	$seo_description .= '。收录于' . $options['site_name'] . $cate['cate_name'] . '分类目录。';

	// 确保描述长度适中
	if (mb_strlen($seo_description, 'UTF-8') > 160) {
		$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
	}

	$smarty->assign('site_description', $seo_description);
	$smarty->assign('site_path', get_sitepath().' &raquo; <a href="?mod=webdir">网站目录</a> &raquo; <a href="?mod=webdir&cid='.$web['cate_id'].'">'.$cate['cate_name'].'</a> &raquo; '.$web['web_name']);
	$smarty->assign('site_rss', get_rssfeed());

	$smarty->assign('web', $web);
	$smarty->assign('user', $user);

	$smarty->assign('cate_id', $cate['cate_id']);
	$smarty->assign('cate_name', $cate['cate_name']);
	$smarty->assign('cate_keywords', $cate['cate_keywords']);
	$smarty->assign('cate_description', $cate['cate_description']);

	/** tags */
	$web_tags = get_format_tags($web['web_tags']);
	$smarty->assign('web_tags', $web_tags);

	// 获取同分类的其他VIP网站
	$related_vip = $DB->fetch_all("SELECT web_id, web_name, web_pic, web_url FROM ".$DB->table('websites')."
	                              WHERE cate_id=".$web['cate_id']." AND web_status=3 AND web_ispay=1 AND web_id!=$web_id
	                              ORDER BY web_istop DESC, web_ctime DESC LIMIT 8");
	
	foreach ($related_vip as &$rel) {
		$rel['web_pic'] = get_webthumb($rel['web_pic']);
		$rel['web_furl'] = format_url($rel['web_url']);
		$rel['web_link'] = '?mod=vip_detail&id='.$rel['web_id'];
	}
	$smarty->assign('related_vip', $related_vip);

	// 上一站下一站（VIP网站）
	$prev_vip = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                           WHERE web_status=3 AND web_ispay=1 AND web_id<$web_id
	                           ORDER BY web_id DESC LIMIT 1");
	$next_vip = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                           WHERE web_status=3 AND web_ispay=1 AND web_id>$web_id
	                           ORDER BY web_id ASC LIMIT 1");

	// 为上一站下一站添加链接
	if ($prev_vip) {
		$prev_vip['web_link'] = '?mod=vip_detail&id='.$prev_vip['web_id'];
	}
	if ($next_vip) {
		$next_vip['web_link'] = '?mod=vip_detail&id='.$next_vip['web_id'];
	}

	$smarty->assign('prev_website', $prev_vip);
	$smarty->assign('next_website', $next_vip);

	// 相关站点显示同分类的正常网站
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}

smarty_output($tempfile, $cache_id);
?>
