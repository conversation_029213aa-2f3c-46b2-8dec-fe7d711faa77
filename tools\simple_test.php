<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 简单授权测试工具
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 定义安全常量
define('IN_HANFOX', true);
define('IN_IWEBDIR', true);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载authcode函数
require(APP_PATH . 'include/function.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单授权测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>简单授权测试</h1>
    
    <?php
    // 测试1：检查授权文件
    echo '<h3>测试1：检查授权文件</h3>';
    $license_file = ROOT_PATH . 'data/license.dat';
    if (file_exists($license_file)) {
        echo '<div class="result success">✅ 授权文件存在: ' . $license_file . '</div>';
        echo '<div class="result info">文件大小: ' . filesize($license_file) . ' 字节</div>';
    } else {
        echo '<div class="result error">❌ 授权文件不存在</div>';
    }
    
    // 测试2：解密测试
    echo '<h3>测试2：解密测试</h3>';
    if (file_exists($license_file)) {
        $file_content = file_get_contents($license_file);
        $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
        
        if (function_exists('authcode')) {
            $decrypted = authcode($file_content, 'DECODE', $license_key);
            if ($decrypted) {
                echo '<div class="result success">✅ 解密成功</div>';
                $data = json_decode($decrypted, true);
                if ($data) {
                    echo '<div class="result success">✅ JSON解析成功</div>';
                    echo '<div class="result info">域名: ' . htmlspecialchars($data['domain']) . '</div>';
                    echo '<div class="result info">到期时间: ' . date('Y-m-d H:i:s', $data['expire_time']) . '</div>';
                } else {
                    echo '<div class="result error">❌ JSON解析失败</div>';
                }
            } else {
                echo '<div class="result error">❌ 解密失败</div>';
            }
        } else {
            echo '<div class="result error">❌ authcode函数不存在</div>';
        }
    }
    
    // 测试3：域名检查
    echo '<h3>测试3：域名检查</h3>';
    $current_domain = '';
    if (isset($_SERVER['HTTP_HOST'])) {
        $current_domain = $_SERVER['HTTP_HOST'];
    } elseif (isset($_SERVER['SERVER_NAME'])) {
        $current_domain = $_SERVER['SERVER_NAME'];
    }
    $current_domain = preg_replace('/:\d+$/', '', $current_domain);
    $current_domain = strtolower($current_domain);
    
    echo '<div class="result info">当前域名: ' . htmlspecialchars($current_domain) . '</div>';
    
    if (isset($data) && isset($data['domain'])) {
        if ($current_domain === $data['domain']) {
            echo '<div class="result success">✅ 域名匹配</div>';
        } else {
            echo '<div class="result error">❌ 域名不匹配</div>';
            echo '<div class="result info">授权域名: ' . htmlspecialchars($data['domain']) . '</div>';
        }
    }
    
    // 测试4：时间检查
    echo '<h3>测试4：时间检查</h3>';
    if (isset($data) && isset($data['expire_time'])) {
        $now = time();
        $expire = $data['expire_time'];
        if ($now <= $expire) {
            echo '<div class="result success">✅ 授权未过期</div>';
            echo '<div class="result info">剩余时间: ' . round(($expire - $now) / 86400) . ' 天</div>';
        } else {
            echo '<div class="result error">❌ 授权已过期</div>';
        }
    }
    ?>
    
    <p><a href="../index.php">返回主站</a></p>
</body>
</html>
