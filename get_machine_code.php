<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 获取机器码工具
 * @Version      : 1.0
 * @Date         : 2025-08-07
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 获取当前域名
$current_domain = '';
if (isset($_SERVER['HTTP_HOST'])) {
    $current_domain = $_SERVER['HTTP_HOST'];
} elseif (isset($_SERVER['SERVER_NAME'])) {
    $current_domain = $_SERVER['SERVER_NAME'];
}

// 移除端口号
$current_domain = preg_replace('/:\d+$/', '', $current_domain);
$current_domain = strtolower($current_domain);

// 生成机器码的简化版本
function generate_simple_machine_code() {
    $factors = array();
    
    // 服务器信息
    $factors[] = $_SERVER['DOCUMENT_ROOT'] ?? '';
    $factors[] = $_SERVER['SERVER_SOFTWARE'] ?? '';
    $factors[] = $_SERVER['HTTP_HOST'] ?? '';
    
    // PHP信息
    $factors[] = PHP_VERSION;
    $factors[] = php_uname('n');
    $factors[] = php_uname('s');
    
    // 文件系统信息
    $factors[] = __FILE__;
    if (function_exists('disk_free_space')) {
        $factors[] = disk_free_space(ROOT_PATH);
    }
    
    // 生成机器码
    $machine_string = implode('|', $factors);
    return strtoupper(md5($machine_string));
}

$machine_code = generate_simple_machine_code();

// 检查授权文件
$license_file = ROOT_PATH . 'data/license.dat';
$license_exists = file_exists($license_file);
$license_status = '未知';

if ($license_exists) {
    // 尝试加载授权验证
    if (file_exists(APP_PATH . 'include/function.php')) {
        try {
            require_once(APP_PATH . 'include/function.php');
            require_once(APP_PATH . 'include/license.php');
            
            $validator = new LicenseValidator();
            if ($validator->validate()) {
                $license_status = '有效';
            } else {
                $license_status = '无效：' . $validator->get_error_message();
            }
        } catch (Exception $e) {
            $license_status = '检查失败：' . $e->getMessage();
        }
    } else {
        $license_status = '无法检查（缺少验证文件）';
    }
} else {
    $license_status = '授权文件不存在';
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>获取机器码 - <?php echo htmlspecialchars($current_domain); ?></title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px; border: 1px solid #dee2e6; }
        .info-item { margin-bottom: 15px; }
        .info-label { font-weight: bold; color: #333; display: block; margin-bottom: 5px; }
        .info-value { color: #666; font-family: monospace; background: #e9ecef; padding: 10px; border-radius: 4px; word-break: break-all; }
        .status-valid { color: #28a745; font-weight: bold; }
        .status-invalid { color: #dc3545; font-weight: bold; }
        .status-missing { color: #ffc107; font-weight: bold; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .copy-btn { background: #6c757d; font-size: 12px; padding: 5px 10px; }
        .copy-btn:hover { background: #545b62; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR机器码获取工具</div>
            <p>为 <?php echo htmlspecialchars($current_domain); ?> 获取授权信息</p>
        </div>
        
        <div class="info-box">
            <div class="info-item">
                <span class="info-label">当前域名：</span>
                <div class="info-value"><?php echo htmlspecialchars($current_domain); ?></div>
            </div>
            
            <div class="info-item">
                <span class="info-label">机器码：</span>
                <div class="info-value">
                    <?php echo $machine_code; ?>
                    <button class="btn copy-btn" onclick="copyToClipboard('<?php echo $machine_code; ?>')">复制</button>
                </div>
            </div>
            
            <div class="info-item">
                <span class="info-label">授权状态：</span>
                <div class="info-value">
                    <span class="<?php 
                        if (strpos($license_status, '有效') !== false) echo 'status-valid';
                        elseif (strpos($license_status, '不存在') !== false) echo 'status-missing';
                        else echo 'status-invalid';
                    ?>">
                        <?php echo htmlspecialchars($license_status); ?>
                    </span>
                </div>
            </div>
            
            <div class="info-item">
                <span class="info-label">系统信息：</span>
                <div class="info-value">
                    PHP版本: <?php echo PHP_VERSION; ?><br>
                    服务器: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?><br>
                    操作系统: <?php echo php_uname('s'); ?><br>
                    主机名: <?php echo php_uname('n'); ?>
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <?php if (!$license_exists): ?>
                <p style="color: #dc3545; font-weight: bold;">⚠️ 此站点尚未授权，请联系开发者获取授权文件</p>
            <?php endif; ?>
            
            <a href="index.php" class="btn">返回首页</a>
            
            <?php if (file_exists('tools/license_test.php')): ?>
                <a href="tools/license_test.php" class="btn">授权测试</a>
            <?php endif; ?>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>获取授权步骤：</h4>
            <ol>
                <li>复制上面的<strong>域名</strong>和<strong>机器码</strong></li>
                <li>发送给开发者申请授权</li>
                <li>收到授权文件后，上传到 <code>data/license.dat</code></li>
                <li>刷新此页面验证授权状态</li>
            </ol>
            
            <h4>联系信息：</h4>
            <p>
                邮箱：<a href="mailto:<EMAIL>?subject=95DIR授权申请&body=域名：<?php echo $current_domain; ?>%0A机器码：<?php echo $machine_code; ?>"><EMAIL></a><br>
                请在邮件中包含域名和机器码信息
            </p>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('机器码已复制到剪贴板');
                });
            } else {
                // 兼容旧浏览器
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('机器码已复制到剪贴板');
            }
        }
    </script>
</body>
</html>
