<?php
/*
 * <AUTHOR> 95DIR高级授权系统
 * @Description  : 95DIR分类目录系统高级授权文件生成工具
 * @Version      : 2.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 加载必要的文件
require(APP_PATH . 'include/function.php');
require(APP_PATH . 'include/security_core.php');

/**
 * 高级授权文件生成器类
 */
class AdvancedLicenseGenerator {
    
    private $security_core;
    
    public function __construct() {
        $this->security_core = SecurityCore::getInstance();
    }
    
    /**
     * 生成高级授权文件
     * @param array $license_info
     * @return string|false
     */
    public function generate_advanced_license($license_info) {
        // 验证必要参数
        $required_fields = ['domain', 'expire_time', 'version', 'license_type'];
        foreach ($required_fields as $field) {
            if (!isset($license_info[$field]) || empty($license_info[$field])) {
                throw new Exception("缺少必要参数: {$field}");
            }
        }
        
        // 生成唯一的授权ID
        $license_id = $this->generate_license_id($license_info);
        
        // 构建授权数据
        $license_data = array(
            'license_id' => $license_id,
            'domain' => $license_info['domain'],
            'expire_time' => $license_info['expire_time'],
            'version' => $license_info['version'],
            'license_type' => $license_info['license_type'],
            'customer_name' => $license_info['customer_name'] ?? '',
            'customer_email' => $license_info['customer_email'] ?? '',
            'machine_code' => $license_info['machine_code'] ?? '',
            'generate_time' => time(),
            'generator_version' => '2.0',
            'security_level' => 'advanced',
            'features' => array(
                'domain_binding' => true,
                'machine_binding' => !empty($license_info['machine_code']),
                'online_verify' => true,
                'anti_debug' => true
            ),
            'checksum' => '',
            'signature' => ''
        );
        
        // 生成校验和
        $license_data['checksum'] = $this->generate_checksum($license_data);
        
        // 生成数字签名
        $license_data['signature'] = $this->generate_advanced_signature($license_data);
        
        // 转换为JSON
        $json_data = json_encode($license_data, JSON_UNESCAPED_UNICODE);
        
        // 使用高级加密
        $encrypted_data = $this->security_core->advanced_encrypt($json_data, 'license_v2');
        
        return $encrypted_data;
    }
    
    /**
     * 生成授权ID
     * @param array $license_info
     * @return string
     */
    private function generate_license_id($license_info) {
        $factors = array(
            $license_info['domain'],
            $license_info['expire_time'],
            $license_info['machine_code'] ?? '',
            microtime(true),
            rand(10000, 99999)
        );
        
        return strtoupper(substr(md5(implode('|', $factors)), 0, 16));
    }
    
    /**
     * 生成校验和
     * @param array $data
     * @return string
     */
    private function generate_checksum($data) {
        // 排除校验和和签名字段
        $check_data = $data;
        unset($check_data['checksum'], $check_data['signature']);
        
        // 递归处理数组
        $check_string = $this->array_to_string($check_data);
        
        return hash('crc32', $check_string);
    }
    
    /**
     * 生成高级数字签名
     * @param array $data
     * @return string
     */
    private function generate_advanced_signature($data) {
        // 排除签名字段
        $sign_data = $data;
        unset($sign_data['signature']);
        
        // 按键名排序
        ksort($sign_data);
        
        // 生成签名字符串
        $sign_string = $this->array_to_string($sign_data);
        
        // 多重哈希签名
        $hash1 = hash('sha256', $sign_string);
        $hash2 = hash('md5', $hash1 . $this->get_signing_key());
        $hash3 = hash('sha1', $hash2 . $data['license_id']);
        
        return strtoupper($hash3);
    }
    
    /**
     * 数组转字符串（递归处理）
     * @param mixed $data
     * @return string
     */
    private function array_to_string($data) {
        if (is_array($data)) {
            $result = '';
            foreach ($data as $key => $value) {
                $result .= $key . '=' . $this->array_to_string($value) . '&';
            }
            return rtrim($result, '&');
        } else {
            return (string)$data;
        }
    }
    
    /**
     * 获取签名密钥
     * @return string
     */
    private function get_signing_key() {
        return $this->security_core->generate_dynamic_key('signature');
    }
    
    /**
     * 保存高级授权文件
     * @param string $encrypted_data
     * @param string $filename
     * @return bool
     */
    public function save_advanced_license_file($encrypted_data, $filename = null) {
        if (!$filename) {
            $filename = ROOT_PATH . 'data/license.dat';
        }
        
        // 确保目录存在
        $dir = dirname($filename);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // 添加文件头标识
        $file_header = "95DIR-LICENSE-V2\n";
        $file_content = $file_header . $encrypted_data;
        
        return file_put_contents($filename, $file_content) !== false;
    }
    
    /**
     * 验证高级授权文件
     * @param string $filename
     * @return array|false
     */
    public function verify_advanced_license_file($filename) {
        if (!file_exists($filename)) {
            return false;
        }
        
        $file_content = file_get_contents($filename);
        
        // 检查文件头
        if (strpos($file_content, "95DIR-LICENSE-V2\n") !== 0) {
            return false;
        }
        
        // 移除文件头
        $encrypted_content = substr($file_content, strlen("95DIR-LICENSE-V2\n"));
        
        // 解密
        $decrypted_content = $this->security_core->advanced_decrypt($encrypted_content, 'license_v2');
        if (!$decrypted_content) {
            return false;
        }
        
        $license_data = json_decode($decrypted_content, true);
        if (!$license_data) {
            return false;
        }
        
        // 验证校验和
        $checksum = $license_data['checksum'];
        $calculated_checksum = $this->generate_checksum($license_data);
        if ($checksum !== $calculated_checksum) {
            return false;
        }
        
        // 验证签名
        $signature = $license_data['signature'];
        $calculated_signature = $this->generate_advanced_signature($license_data);
        if ($signature !== $calculated_signature) {
            return false;
        }
        
        return $license_data;
    }
    
    /**
     * 生成授权报告
     * @param array $license_data
     * @return string
     */
    public function generate_license_report($license_data) {
        $report = "=== 95DIR高级授权文件报告 ===\n\n";
        $report .= "授权ID: " . $license_data['license_id'] . "\n";
        $report .= "授权域名: " . $license_data['domain'] . "\n";
        $report .= "到期时间: " . date('Y-m-d H:i:s', $license_data['expire_time']) . "\n";
        $report .= "授权版本: " . $license_data['version'] . "\n";
        $report .= "授权类型: " . $license_data['license_type'] . "\n";
        $report .= "客户信息: " . $license_data['customer_name'] . "\n";
        $report .= "客户邮箱: " . $license_data['customer_email'] . "\n";
        $report .= "机器码: " . ($license_data['machine_code'] ?: '未绑定') . "\n";
        $report .= "生成时间: " . date('Y-m-d H:i:s', $license_data['generate_time']) . "\n";
        $report .= "安全级别: " . $license_data['security_level'] . "\n";
        $report .= "生成器版本: " . $license_data['generator_version'] . "\n";
        
        $report .= "\n=== 功能特性 ===\n";
        foreach ($license_data['features'] as $feature => $enabled) {
            $report .= ucfirst(str_replace('_', ' ', $feature)) . ": " . ($enabled ? '启用' : '禁用') . "\n";
        }
        
        $report .= "\n=== 安全信息 ===\n";
        $report .= "校验和: " . $license_data['checksum'] . "\n";
        $report .= "数字签名: " . substr($license_data['signature'], 0, 16) . "...\n";
        
        return $report;
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $generator = new AdvancedLicenseGenerator();
        
        // 获取表单数据
        $license_info = array(
            'domain' => trim($_POST['domain']),
            'expire_time' => strtotime($_POST['expire_date']),
            'version' => trim($_POST['version']),
            'license_type' => trim($_POST['license_type']),
            'customer_name' => trim($_POST['customer_name']),
            'customer_email' => trim($_POST['customer_email']),
            'machine_code' => trim($_POST['machine_code'])
        );
        
        // 生成高级授权文件
        $encrypted_data = $generator->generate_advanced_license($license_info);
        
        // 保存到文件
        $filename = ROOT_PATH . 'data/license_advanced_' . date('Ymd_His') . '.dat';
        $generator->save_advanced_license_file($encrypted_data, $filename);
        
        // 也保存为默认授权文件
        $generator->save_advanced_license_file($encrypted_data);
        
        // 验证生成的文件
        $verify_result = $generator->verify_advanced_license_file(ROOT_PATH . 'data/license.dat');
        
        if ($verify_result) {
            $success_message = "高级授权文件生成成功！<br>文件保存位置：{$filename}";
            $license_report = $generator->generate_license_report($verify_result);
        } else {
            $error_message = "授权文件验证失败！";
        }
        
    } catch (Exception $e) {
        $error_message = "生成失败：" . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR高级授权文件生成工具</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .security-badge { background: #28a745; color: #fff; padding: 5px 15px; border-radius: 20px; font-size: 12px; margin-left: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .form-group textarea { height: 80px; resize: vertical; }
        .btn { background: #007bff; color: #fff; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .help-text { font-size: 12px; color: #666; margin-top: 5px; }
        .row { display: flex; gap: 20px; }
        .col { flex: 1; }
        .license-report { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-top: 20px; font-family: monospace; font-size: 12px; white-space: pre-line; }
        .security-features { background: #e7f3ff; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 5px 0; }
        .feature-list li:before { content: "🔒 "; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                95DIR高级授权文件生成工具
                <span class="security-badge">高级安全版</span>
            </div>
            <p>生成具有高级安全保护的授权文件</p>
        </div>
        
        <div class="security-features">
            <h4>🛡️ 高级安全特性</h4>
            <ul class="feature-list">
                <li>动态密钥生成（基于服务器特征）</li>
                <li>多重加密算法（AES-256 + 自定义RC4 + 混淆编码）</li>
                <li>数字签名验证（SHA256 + MD5 + SHA1）</li>
                <li>文件完整性校验（CRC32校验和）</li>
                <li>反调试和反分析保护</li>
                <li>运行时环境检测</li>
                <li>随机验证点触发</li>
            </ul>
        </div>
        
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <?php echo $success_message; ?>
            <?php if (isset($license_report)): ?>
            <div class="license-report"><?php echo htmlspecialchars($license_report); ?></div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-error"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <form method="post">
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="domain">授权域名 *</label>
                        <input type="text" id="domain" name="domain" required placeholder="例如：www.example.com 或 *.example.com">
                        <div class="help-text">支持通配符域名，建议使用精确域名以提高安全性</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="expire_date">到期时间 *</label>
                        <input type="datetime-local" id="expire_date" name="expire_date" required>
                        <div class="help-text">授权到期时间，建议不要设置过长时间</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="version">授权版本 *</label>
                        <select id="version" name="version" required>
                            <option value="">请选择版本</option>
                            <option value="95DIR-v3.0">95DIR-v3.0（推荐）</option>
                            <option value="*">所有版本（不推荐）</option>
                        </select>
                        <div class="help-text">建议绑定具体版本以提高安全性</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="license_type">授权类型 *</label>
                        <select id="license_type" name="license_type" required>
                            <option value="">请选择类型</option>
                            <option value="commercial">商业授权</option>
                            <option value="personal">个人授权</option>
                            <option value="trial">试用授权</option>
                            <option value="developer">开发者授权</option>
                        </select>
                        <div class="help-text">不同类型的授权具有不同的功能限制</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="customer_name">客户姓名 *</label>
                        <input type="text" id="customer_name" name="customer_name" required placeholder="客户姓名或公司名称">
                        <div class="help-text">必填，用于授权追踪和技术支持</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="customer_email">客户邮箱 *</label>
                        <input type="email" id="customer_email" name="customer_email" required placeholder="<EMAIL>">
                        <div class="help-text">必填，用于授权验证和联系客户</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="machine_code">机器码（强烈推荐）</label>
                <input type="text" id="machine_code" name="machine_code" placeholder="客户提供的32位机器码">
                <div class="help-text">强烈建议绑定机器码，可大幅提高安全性</div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">生成高级授权文件</button>
            </div>
        </form>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>🔐 高级安全说明：</h4>
            <ol>
                <li><strong>动态加密</strong>：每个授权文件使用不同的加密密钥</li>
                <li><strong>多重验证</strong>：域名、机器码、时间、版本四重验证</li>
                <li><strong>防篡改</strong>：数字签名和校验和双重保护</li>
                <li><strong>反调试</strong>：自动检测调试和分析环境</li>
                <li><strong>随机验证</strong>：系统运行时随机触发验证</li>
                <li><strong>环境绑定</strong>：授权与特定服务器环境绑定</li>
            </ol>
            
            <p><strong>安全等级：⭐⭐⭐⭐⭐ 很难破解</strong></p>
        </div>
    </div>
</body>
</html>
