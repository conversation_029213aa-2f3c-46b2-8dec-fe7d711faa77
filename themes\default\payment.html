<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
	<div id="mainbox" class="clearfix mtop10">
		<div id="subbox">
		<div style="line-height: 25px; padding: 10px 20px;">
        <div class="payment-header">
            <h2>💳 支付页面</h2>
            <p>请使用微信扫码支付完成网站收录</p>
        </div>

        <div class="payment-container">
            <div class="payment-info">
                <div class="order-info">
                    <h3>📋 订单信息</h3>
                    <table class="order-table">
                        <tr>
                            <td><strong>订单号：</strong></td>
                            <td>{#$order.order_no#}</td>
                        </tr>
                        <tr>
                            <td><strong>服务类型：</strong></td>
                            <td>{#$price_info.name#}</td>
                        </tr>
                        <tr>
                            <td><strong>网站名称：</strong></td>
                            <td>{#$order.web_name#}</td>
                        </tr>
                        <tr>
                            <td><strong>网站地址：</strong></td>
                            <td>{#$order.web_url#}</td>
                        </tr>
                        <tr>
                            <td><strong>支付金额：</strong></td>
                            <td class="price">¥{#$order.amount#}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="payment-status">
                    <h3>⏰ 支付状态</h3>
                    <div id="status-display">
                        <div class="status-waiting">
                            <span class="status-icon">⏳</span>
                            <span class="status-text">等待支付中...</span>
                        </div>
                        <div class="countdown">
                            订单将在 <span id="countdown-timer">30:00</span> 后过期
                        </div>
                        <div style="margin-top: 10px;">
                            <button id="check-status-btn" onclick="manualCheckStatus()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">手动检查支付状态</button>
                        </div>
                        <div id="debug-info" style="font-size: 12px; color: #666; margin-top: 10px;"></div>
                    </div>
                </div>
            </div>
            
            <div class="payment-qr">
                <div class="qr-container">
                    <h3>📱 微信扫码支付</h3>
                    <div id="qr-code-container">
                        {#if $order.qr_code#}
                            <img id="payment-qrcode" src="" alt="支付二维码" style="width: 200px; height: 200px; border: 1px solid #ddd; border-radius: 8px;">
                            <p class="qr-tip">请使用微信扫描上方二维码完成支付</p>
                        {#else#}
                            <div class="loading">正在生成支付二维码...</div>
                        {#/if#}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="payment-tips">
            <h4>💡 支付说明</h4>
            <ul>
                <li>请在订单过期前完成支付，过期后需要重新提交</li>
                <li>支付完成后系统将自动处理您的网站收录</li>
                <li>如遇到支付问题，请联系客服</li>
                <li>支付成功后，页面将自动跳转</li>
            </ul>
        </div>
		</div>
		</div>
    </div>
    {#include file="footer.html"#}
</div>

<style>
/* 支付页面样式 */
.payment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.payment-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: bold;
}

.payment-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.payment-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .payment-container {
        grid-template-columns: 1fr;
    }
}

.payment-info {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.order-info, .payment-status {
    margin-bottom: 20px;
}

.order-info h3, .payment-status h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.order-table {
    width: 100%;
    border-collapse: collapse;
}

.order-table td {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-table td:first-child {
    width: 100px;
    color: #666;
}

.order-table .price {
    color: #e74c3c;
    font-size: 18px;
    font-weight: bold;
}

.payment-status {
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;
}

.status-waiting {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-icon {
    font-size: 20px;
    margin-right: 10px;
}

.status-text {
    font-size: 14px;
    color: #666;
}

.status-success .status-icon {
    color: #27ae60;
}

.status-success .status-text {
    color: #27ae60;
    font-weight: bold;
}

.countdown {
    font-size: 12px;
    color: #e74c3c;
}

.payment-qr {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.payment-qr h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 16px;
}

#qrcode {
    margin: 20px auto;
    display: inline-block;
}

.qr-tip {
    font-size: 12px;
    color: #666;
    margin-top: 10px;
}

.loading {
    padding: 40px;
    color: #666;
    font-size: 14px;
}

.payment-tips {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.payment-tips h4 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 14px;
}

.payment-tips ul {
    margin: 0;
    padding-left: 20px;
}

.payment-tips li {
    font-size: 12px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 5px;
}
</style>

<script>
// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 生成支付二维码
    {#if $order.qr_code#}
    const qrCodeData = '{#$order.qr_code#}';
    console.log('二维码数据:', qrCodeData);

    if (qrCodeData) {
        // 使用在线API生成二维码（最稳定的方案）
        const encodedData = encodeURIComponent(qrCodeData);
        const apiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedData}`;

        const qrImg = document.getElementById('payment-qrcode');
        if (qrImg) {
            qrImg.src = apiUrl;
            qrImg.onload = function() {
                console.log('支付二维码加载成功');
            };
            qrImg.onerror = function() {
                console.error('支付二维码加载失败');
                this.parentNode.innerHTML = '<div class="loading">二维码生成失败，请刷新页面重试</div>';
            };
        }
    }
    {#else#}
    console.log('没有二维码数据');
    {#/if#}
    
    // 倒计时
    let remainingTime = {#$remaining_time#};
    const countdownTimer = document.getElementById('countdown-timer');
    
    function updateCountdown() {
        if (remainingTime <= 0) {
            alert('订单已过期，请重新提交！');
            window.location.href = '?mod=quicksubmit';
            return;
        }
        
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        countdownTimer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        remainingTime--;
    }
    
    // 立即更新一次，然后每秒更新
    updateCountdown();
    const countdownInterval = setInterval(updateCountdown, 1000);
    
    // 轮询查询支付状态
    const orderNo = '{#$order.order_no#}';
    let pollCount = 0;
    let pollInterval;
    const maxPollCount = 720; // 最多轮询60分钟（每5秒一次）

    function updateDebugInfo(message) {
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
            const time = new Date().toLocaleTimeString();
            debugInfo.innerHTML = `[${time}] ${message}`;
        }
    }

    function checkPaymentStatus() {
        if (pollCount >= maxPollCount) {
            clearInterval(pollInterval);
            updateDebugInfo('轮询已停止（超时）');
            return;
        }

        pollCount++;
        updateDebugInfo(`正在查询支付状态... (${pollCount}/${maxPollCount})`);

        fetch('?mod=payment_check&order_no=' + orderNo)
            .then(response => {
                console.log('支付状态查询响应:', response);
                return response.json();
            })
            .then(data => {
                console.log('支付状态查询结果:', data);
                updateDebugInfo(`查询结果: ${data.message}`);

                if (data.success && data.paid) {
                    // 支付成功
                    clearInterval(pollInterval);
                    clearInterval(countdownInterval);

                    updateDebugInfo('支付成功！正在跳转...');

                    const statusDisplay = document.getElementById('status-display');
                    if (statusDisplay) {
                        statusDisplay.innerHTML = \`
                            <div class="status-success">
                                <span class="status-icon">✅</span>
                                <span class="status-text">支付成功！正在处理...</span>
                            </div>
                            <div style="margin-top: 10px; color: green;">
                                网站已提交审核，即将跳转到首页...
                            </div>
                        \`;
                    }

                    // 延迟跳转
                    setTimeout(function() {
                        alert('支付成功！您的网站已提交审核。');
                        window.location.href = '?mod=index';
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('查询支付状态失败:', error);
                updateDebugInfo('查询失败: ' + error.message);
            });
    }

    // 手动检查支付状态
    function manualCheckStatus() {
        updateDebugInfo('手动检查支付状态...');
        checkPaymentStatus();
    }

    // 立即查询一次
    checkPaymentStatus();

    // 每5秒查询一次支付状态
    pollInterval = setInterval(checkPaymentStatus, 5000);

    // 页面获得焦点时立即查询一次
    window.addEventListener('focus', function() {
        updateDebugInfo('页面获得焦点，立即查询...');
        checkPaymentStatus();
    });

    // 将函数暴露到全局作用域
    window.manualCheckStatus = manualCheckStatus;
});
</script>

</body>
</html>
