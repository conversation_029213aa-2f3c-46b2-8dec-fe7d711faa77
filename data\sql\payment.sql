-- 支付订单表（用于快速提交付费审核）
CREATE TABLE IF NOT EXISTS `dir_payment_orders` (
  `order_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `trade_no` varchar(64) NOT NULL DEFAULT '' COMMENT '第三方交易号',
  `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
  `pay_type` varchar(20) NOT NULL DEFAULT 'wxpay' COMMENT '支付方式',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '支付状态：0=待支付，1=已支付，2=已取消',
  `web_name` varchar(100) NOT NULL DEFAULT '' COMMENT '网站名称',
  `web_url` varchar(255) NOT NULL DEFAULT '' COMMENT '网站地址',
  `web_tags` varchar(100) NOT NULL DEFAULT '' COMMENT '网站标签',
  `web_intro` text NOT NULL COMMENT '网站简介',
  `web_owner` varchar(50) NOT NULL DEFAULT '' COMMENT '站长姓名',
  `web_email` varchar(50) NOT NULL DEFAULT '' COMMENT '联系邮箱',
  `cate_id` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `qr_code` text NOT NULL COMMENT '支付二维码内容',
  `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单过期时间',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `web_id` (`web_id`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`),
  KEY `expire_time` (`expire_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付订单表';

-- 支付配置表
CREATE TABLE IF NOT EXISTS `dir_payment_config` (
  `config_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` varchar(50) NOT NULL DEFAULT '',
  `merchant_key` varchar(100) NOT NULL DEFAULT '',
  `api_url` varchar(255) NOT NULL DEFAULT '',
  `notify_url` varchar(255) NOT NULL DEFAULT '',
  `return_url` varchar(255) NOT NULL DEFAULT '',
  `recommend_price` decimal(10,2) NOT NULL DEFAULT '10.00',
  `quick_review_price` decimal(10,2) NOT NULL DEFAULT '20.00',
  `vip_price` decimal(10,2) NOT NULL DEFAULT '50.00',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`config_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付配置表';

-- 插入默认配置
INSERT INTO `dir_payment_config` (`merchant_id`, `merchant_key`, `api_url`, `notify_url`, `return_url`, `recommend_price`, `quick_review_price`, `vip_price`, `status`, `create_time`) VALUES
('1007', 'ScxVfWEfqMqJlLy5jkLROhSmWlVgEUds', 'https://pay.chaobie.com/', '', '', 10.00, 20.00, 50.00, 1, UNIX_TIMESTAMP());
