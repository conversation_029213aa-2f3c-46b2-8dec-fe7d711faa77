<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{$pagetitle} - 95DIR管理后台</title>
    <link rel="stylesheet" href="templates/style.css">
    <style>
        .license-status { padding: 15px; margin: 20px 0; border-radius: 6px; }
        .status-valid { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-invalid { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-missing { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; border: 1px solid #dee2e6; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; }
        .btn-group { margin: 10px 0; }
        .btn { padding: 8px 16px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: #fff; }
        .btn-success { background: #28a745; color: #fff; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: #fff; }
        .btn:hover { opacity: 0.8; }
        .upload-form { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .machine-code { font-family: monospace; background: #e9ecef; padding: 10px; border-radius: 4px; word-break: break-all; }
        .tab-container { margin: 20px 0; }
        .tab-nav { border-bottom: 1px solid #dee2e6; margin-bottom: 20px; }
        .tab-nav a { display: inline-block; padding: 10px 20px; margin-right: 5px; text-decoration: none; color: #495057; border-bottom: 2px solid transparent; }
        .tab-nav a.active { color: #007bff; border-bottom-color: #007bff; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>{$pagetitle}</h1>
        
        <!-- 授权状态显示 -->
        {if $license_exists}
            {if $license_valid}
                <div class="license-status status-valid">
                    <h3>✓ 授权状态：有效</h3>
                    <p>系统授权验证通过，可以正常使用所有功能。</p>
                </div>
            {else}
                <div class="license-status status-invalid">
                    <h3>✗ 授权状态：无效</h3>
                    <p>{$error_message}</p>
                </div>
            {/if}
        {else}
            <div class="license-status status-missing">
                <h3>⚠ 授权状态：未授权</h3>
                <p>系统未检测到有效的授权文件，请上传授权文件或联系开发者获取授权。</p>
            </div>
        {/if}
        
        <!-- 选项卡导航 -->
        <div class="tab-container">
            <div class="tab-nav">
                <a href="#status" class="tab-link active" onclick="showTab('status')">授权状态</a>
                <a href="#upload" class="tab-link" onclick="showTab('upload')">上传授权</a>
                <a href="#machine" class="tab-link" onclick="showTab('machine')">机器信息</a>
                <a href="#tools" class="tab-link" onclick="showTab('tools')">管理工具</a>
            </div>
            
            <!-- 授权状态选项卡 -->
            <div id="status-tab" class="tab-content active">
                <div class="info-grid">
                    <!-- 系统信息 -->
                    <div class="info-box">
                        <h4>系统信息</h4>
                        <div class="info-item">
                            <span class="info-label">当前域名：</span>
                            <span class="info-value">{$system_info.domain}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">系统版本：</span>
                            <span class="info-value">{$system_info.version}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">PHP版本：</span>
                            <span class="info-value">{$system_info.php_version}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">服务器软件：</span>
                            <span class="info-value">{$system_info.server_software}</span>
                        </div>
                    </div>
                    
                    <!-- 授权信息 -->
                    {if $license_info}
                    <div class="info-box">
                        <h4>授权信息</h4>
                        <div class="info-item">
                            <span class="info-label">授权域名：</span>
                            <span class="info-value">{$license_info.domain}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">到期时间：</span>
                            <span class="info-value">{$license_info.expire_time|date_format:'%Y-%m-%d %H:%M:%S'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">授权版本：</span>
                            <span class="info-value">{$license_info.version}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">授权类型：</span>
                            <span class="info-value">{$license_info.license_type}</span>
                        </div>
                        {if $license_info.customer_name}
                        <div class="info-item">
                            <span class="info-label">客户信息：</span>
                            <span class="info-value">{$license_info.customer_name}</span>
                        </div>
                        {/if}
                    </div>
                    {/if}
                </div>
            </div>
            
            <!-- 上传授权选项卡 -->
            <div id="upload-tab" class="tab-content">
                <div class="upload-form">
                    <h4>上传授权文件</h4>
                    <form action="license_manager.php?act=upload" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="license_file">选择授权文件（license.dat）：</label>
                            <input type="file" id="license_file" name="license_file" accept=".dat" required>
                        </div>
                        <div class="btn-group">
                            <button type="submit" class="btn btn-primary">上传授权文件</button>
                        </div>
                    </form>
                    
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #666; font-size: 14px;">
                        <h5>说明：</h5>
                        <ul>
                            <li>请确保上传的是有效的授权文件</li>
                            <li>授权文件通常以 .dat 为扩展名</li>
                            <li>上传后系统会自动验证文件有效性</li>
                            <li>如有问题请联系技术支持</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 机器信息选项卡 -->
            <div id="machine-tab" class="tab-content">
                <div class="info-box">
                    <h4>机器码信息</h4>
                    <p>请将以下信息提供给开发者以获取授权：</p>
                    
                    <div class="info-item">
                        <span class="info-label">域名：</span>
                        <span class="info-value">{$system_info.domain}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">机器码：</span>
                        <div class="machine-code">{$machine_code}</div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">系统版本：</span>
                        <span class="info-value">{$system_info.version}</span>
                    </div>
                    
                    <div class="btn-group">
                        <button onclick="copyMachineCode()" class="btn btn-success">复制机器码</button>
                        <a href="mailto:<EMAIL>?subject=95DIR授权申请&body=域名：{$system_info.domain}%0A机器码：{$machine_code}%0A系统版本：{$system_info.version}" class="btn btn-primary">发送邮件申请</a>
                    </div>
                </div>
            </div>
            
            <!-- 管理工具选项卡 -->
            <div id="tools-tab" class="tab-content">
                <div class="info-box">
                    <h4>管理工具</h4>
                    
                    <div class="btn-group">
                        <a href="license_manager.php?act=online_verify" class="btn btn-primary" onclick="return confirm('确定要进行在线验证吗？')">在线验证</a>
                        <a href="license_manager.php?act=integrity_check" class="btn btn-warning" onclick="return confirm('确定要进行文件完整性检查吗？')">完整性检查</a>
                        {if $license_exists}
                        <a href="license_manager.php?act=delete" class="btn btn-danger" onclick="return confirm('确定要删除当前授权文件吗？')">删除授权</a>
                        {/if}
                    </div>
                    
                    <div style="margin-top: 20px; color: #666; font-size: 14px;">
                        <h5>工具说明：</h5>
                        <ul>
                            <li><strong>在线验证：</strong>连接授权服务器验证当前授权状态</li>
                            <li><strong>完整性检查：</strong>检查系统核心文件是否被篡改</li>
                            <li><strong>删除授权：</strong>删除当前的授权文件（谨慎操作）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="main.php" class="btn btn-primary">返回管理首页</a>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // 隐藏所有选项卡内容
            var contents = document.querySelectorAll('.tab-content');
            contents.forEach(function(content) {
                content.classList.remove('active');
            });
            
            // 移除所有选项卡链接的active类
            var links = document.querySelectorAll('.tab-link');
            links.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // 显示选中的选项卡内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 添加选中链接的active类
            event.target.classList.add('active');
        }
        
        function copyMachineCode() {
            var machineCode = '{$machine_code}';
            if (navigator.clipboard) {
                navigator.clipboard.writeText(machineCode).then(function() {
                    alert('机器码已复制到剪贴板');
                });
            } else {
                // 兼容旧浏览器
                var textArea = document.createElement('textarea');
                textArea.value = machineCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('机器码已复制到剪贴板');
            }
        }
    </script>
</body>
</html>
