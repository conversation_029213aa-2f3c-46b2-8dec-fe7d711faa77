<?php
/*
 * <AUTHOR> 95DIR高级安全系统
 * @Description  : 95DIR分类目录系统高级安全核心模块
 * @Version      : 2.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 * 
 * 警告：此文件包含关键安全代码，请勿随意修改！
 */

if (!defined('IN_HANFOX')) {
    exit('Access Denied');
}

/**
 * 高级安全核心类
 */
class SecurityCore {
    
    private static $instance = null;
    private $dynamic_keys = array();
    private $security_checks = array();
    private $runtime_hash = '';
    
    private function __construct() {
        $this->runtime_hash = $this->generate_runtime_hash();
        $this->init_security_checks();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 生成动态密钥
     * @param string $context
     * @return string
     */
    public function generate_dynamic_key($context = 'default') {
        if (isset($this->dynamic_keys[$context])) {
            return $this->dynamic_keys[$context];
        }
        
        // 收集服务器特征
        $server_factors = array(
            $_SERVER['DOCUMENT_ROOT'] ?? '',
            $_SERVER['SERVER_SOFTWARE'] ?? '',
            $_SERVER['HTTP_HOST'] ?? '',
            php_uname('n'),
            PHP_VERSION,
            __FILE__,
            filemtime(__FILE__),
            filesize(__FILE__)
        );
        
        // 添加文件系统特征
        $fs_factors = array();
        if (function_exists('disk_free_space')) {
            $fs_factors[] = disk_free_space(ROOT_PATH);
        }
        if (function_exists('disk_total_space')) {
            $fs_factors[] = disk_total_space(ROOT_PATH);
        }
        
        // 生成基础密钥
        $base_key = hash('sha256', implode('|', array_merge($server_factors, $fs_factors)));
        
        // 添加上下文相关的盐值
        $context_salt = hash('md5', $context . $this->runtime_hash);
        
        // 生成最终密钥
        $final_key = hash('sha512', $base_key . $context_salt . $this->get_secret_salt());
        
        $this->dynamic_keys[$context] = $final_key;
        return $final_key;
    }
    
    /**
     * 获取秘密盐值（混淆处理）
     * @return string
     */
    private function get_secret_salt() {
        // 使用多种方式混淆真实的盐值
        $parts = array(
            chr(57).chr(53).chr(68).chr(73).chr(82), // "95DIR"
            chr(95).chr(83).chr(69).chr(67).chr(85).chr(82).chr(73).chr(84).chr(89), // "_SECURITY"
            chr(95).chr(75).chr(69).chr(89).chr(95), // "_KEY_"
            date('Y') . date('m') // 年月
        );
        
        return implode('', $parts) . $this->calculate_checksum();
    }
    
    /**
     * 计算校验和
     * @return string
     */
    private function calculate_checksum() {
        $factors = array(
            strlen(__FILE__),
            filemtime(__FILE__),
            md5_file(__FILE__)
        );
        return substr(md5(implode('', $factors)), 0, 8);
    }
    
    /**
     * 生成运行时哈希
     * @return string
     */
    private function generate_runtime_hash() {
        $runtime_factors = array(
            microtime(true),
            memory_get_usage(),
            getmypid(),
            $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)
        );
        return md5(implode('', $runtime_factors));
    }
    
    /**
     * 高级加密函数
     * @param string $data
     * @param string $context
     * @return string
     */
    public function advanced_encrypt($data, $context = 'license') {
        $key = $this->generate_dynamic_key($context);
        
        // 第一层：AES加密（如果支持）
        if (function_exists('openssl_encrypt')) {
            $iv = openssl_random_pseudo_bytes(16);
            $encrypted = openssl_encrypt($data, 'AES-256-CBC', substr($key, 0, 32), 0, $iv);
            $data = base64_encode($iv . $encrypted);
        }
        
        // 第二层：自定义RC4加密
        $data = $this->custom_rc4_encrypt($data, substr($key, 32, 32));
        
        // 第三层：混淆编码
        $data = $this->obfuscate_encode($data);
        
        return $data;
    }
    
    /**
     * 高级解密函数
     * @param string $data
     * @param string $context
     * @return string|false
     */
    public function advanced_decrypt($data, $context = 'license') {
        try {
            $key = $this->generate_dynamic_key($context);
            
            // 第三层：混淆解码
            $data = $this->obfuscate_decode($data);
            if ($data === false) return false;
            
            // 第二层：自定义RC4解密
            $data = $this->custom_rc4_decrypt($data, substr($key, 32, 32));
            if ($data === false) return false;
            
            // 第一层：AES解密（如果支持）
            if (function_exists('openssl_decrypt')) {
                $data = base64_decode($data);
                $iv = substr($data, 0, 16);
                $encrypted = substr($data, 16);
                $data = openssl_decrypt($encrypted, 'AES-256-CBC', substr($key, 0, 32), 0, $iv);
            }
            
            return $data;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 自定义RC4加密
     * @param string $data
     * @param string $key
     * @return string
     */
    private function custom_rc4_encrypt($data, $key) {
        // 增强的RC4算法
        $key = hash('sha256', $key);
        $s = range(0, 255);
        $j = 0;
        
        // KSA
        for ($i = 0; $i < 256; $i++) {
            $j = ($j + $s[$i] + ord($key[$i % strlen($key)])) % 256;
            $temp = $s[$i];
            $s[$i] = $s[$j];
            $s[$j] = $temp;
        }
        
        // PRGA
        $i = $j = 0;
        $result = '';
        for ($k = 0; $k < strlen($data); $k++) {
            $i = ($i + 1) % 256;
            $j = ($j + $s[$i]) % 256;
            $temp = $s[$i];
            $s[$i] = $s[$j];
            $s[$j] = $temp;
            $result .= chr(ord($data[$k]) ^ $s[($s[$i] + $s[$j]) % 256]);
        }
        
        return base64_encode($result);
    }
    
    /**
     * 自定义RC4解密
     * @param string $data
     * @param string $key
     * @return string|false
     */
    private function custom_rc4_decrypt($data, $key) {
        $data = base64_decode($data);
        if ($data === false) return false;
        
        return $this->custom_rc4_encrypt($data, $key); // RC4是对称的
    }
    
    /**
     * 混淆编码
     * @param string $data
     * @return string
     */
    private function obfuscate_encode($data) {
        $encoded = base64_encode($data);
        $result = '';
        
        // 字符替换混淆
        $replace_map = array(
            'A' => 'Z', 'B' => 'Y', 'C' => 'X', 'D' => 'W',
            'a' => 'z', 'b' => 'y', 'c' => 'x', 'd' => 'w',
            '0' => '9', '1' => '8', '2' => '7', '3' => '6'
        );
        
        for ($i = 0; $i < strlen($encoded); $i++) {
            $char = $encoded[$i];
            $result .= isset($replace_map[$char]) ? $replace_map[$char] : $char;
        }
        
        // 添加随机前缀和后缀
        $prefix = substr(md5(microtime()), 0, 4);
        $suffix = substr(md5(microtime()), -4);
        
        return $prefix . $result . $suffix;
    }
    
    /**
     * 混淆解码
     * @param string $data
     * @return string|false
     */
    private function obfuscate_decode($data) {
        if (strlen($data) < 8) return false;
        
        // 移除前缀和后缀
        $data = substr($data, 4, -4);
        
        // 字符替换还原
        $replace_map = array(
            'Z' => 'A', 'Y' => 'B', 'X' => 'C', 'W' => 'D',
            'z' => 'a', 'y' => 'b', 'x' => 'c', 'w' => 'd',
            '9' => '0', '8' => '1', '7' => '2', '6' => '3'
        );
        
        $result = '';
        for ($i = 0; $i < strlen($data); $i++) {
            $char = $data[$i];
            $result .= isset($replace_map[$char]) ? $replace_map[$char] : $char;
        }
        
        return base64_decode($result);
    }
    
    /**
     * 初始化安全检查
     */
    private function init_security_checks() {
        $this->security_checks = array(
            'file_integrity' => false,
            'runtime_environment' => false,
            'debug_detection' => false,
            'code_modification' => false
        );
    }
    
    /**
     * 执行全面安全检查
     * @return bool
     */
    public function comprehensive_security_check() {
        // 检查1：文件完整性
        $this->security_checks['file_integrity'] = $this->check_file_integrity();
        
        // 检查2：运行环境
        $this->security_checks['runtime_environment'] = $this->check_runtime_environment();
        
        // 检查3：调试检测
        $this->security_checks['debug_detection'] = $this->check_debug_environment();
        
        // 检查4：代码修改检测
        $this->security_checks['code_modification'] = $this->check_code_modification();
        
        // 所有检查都必须通过
        foreach ($this->security_checks as $check => $result) {
            if (!$result) {
                $this->trigger_security_response($check);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查文件完整性
     * @return bool
     */
    private function check_file_integrity() {
        $critical_files = array(
            'source/include/license.php',
            'source/include/security_core.php',
            'source/init.php'
        );
        
        foreach ($critical_files as $file) {
            $file_path = ROOT_PATH . $file;
            if (!file_exists($file_path)) {
                return false;
            }
            
            // 检查文件修改时间是否异常
            $mtime = filemtime($file_path);
            if (time() - $mtime < 60) { // 1分钟内被修改
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查运行环境
     * @return bool
     */
    private function check_runtime_environment() {
        // 检查是否在调试模式
        if (ini_get('display_errors') == '1') {
            return false;
        }
        
        // 检查是否有调试扩展
        $debug_extensions = array('xdebug', 'zend_debugger');
        foreach ($debug_extensions as $ext) {
            if (extension_loaded($ext)) {
                return false;
            }
        }
        
        // 检查内存使用是否异常
        $memory_usage = memory_get_usage(true);
        if ($memory_usage > 50 * 1024 * 1024) { // 超过50MB
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查调试环境
     * @return bool
     */
    private function check_debug_environment() {
        // 检查调试相关的GET/POST参数
        $debug_params = array('debug', 'test', 'dev', 'trace', 'profile');
        foreach ($debug_params as $param) {
            if (isset($_GET[$param]) || isset($_POST[$param])) {
                return false;
            }
        }
        
        // 检查User-Agent
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $ua = strtolower($_SERVER['HTTP_USER_AGENT']);
            $suspicious_ua = array('curl', 'wget', 'postman', 'insomnia', 'burp', 'fiddler');
            foreach ($suspicious_ua as $tool) {
                if (strpos($ua, $tool) !== false) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 检查代码修改
     * @return bool
     */
    private function check_code_modification() {
        // 检查关键函数是否被重定义
        if (!function_exists('verify_system_license')) {
            return false;
        }
        
        // 检查关键常量
        if (!defined('ROOT_PATH') || !defined('APP_PATH')) {
            return false;
        }
        
        // 检查当前文件的完整性
        $current_hash = md5_file(__FILE__);
        $expected_patterns = array(
            'class SecurityCore',
            'generate_dynamic_key',
            'comprehensive_security_check'
        );
        
        $file_content = file_get_contents(__FILE__);
        foreach ($expected_patterns as $pattern) {
            if (strpos($file_content, $pattern) === false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 触发安全响应
     * @param string $check_type
     */
    private function trigger_security_response($check_type) {
        // 记录安全事件
        $this->log_security_event($check_type);
        
        // 清除输出缓冲
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // 发送假的错误响应
        http_response_code(500);
        header('Content-Type: text/html; charset=utf-8');
        
        // 显示假的数据库错误
        $fake_errors = array(
            'MySQL server has gone away',
            'Table \'database.table\' doesn\'t exist',
            'Access denied for user \'root\'@\'localhost\'',
            'Can\'t connect to MySQL server on \'localhost\'',
            'Unknown database \'website_db\''
        );
        
        $error = $fake_errors[array_rand($fake_errors)];
        
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Database Error</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .error-container { max-width: 600px; margin: 0 auto; background: #fff; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-title { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
        .error-message { color: #666; line-height: 1.6; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-title">Database Connection Error</div>
        <div class="error-message">
            Error: ' . htmlspecialchars($error) . '<br>
            Error Code: ' . rand(1000, 9999) . '<br>
            Time: ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
        
        exit;
    }
    
    /**
     * 记录安全事件
     * @param string $event_type
     */
    private function log_security_event($event_type) {
        $log_data = array(
            'timestamp' => time(),
            'event_type' => $event_type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        );
        
        $log_file = ROOT_PATH . 'data/security.log';
        $log_entry = date('Y-m-d H:i:s') . ' [' . $event_type . '] ' . json_encode($log_data) . "\n";
        
        @file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 随机验证触发器
     * @return bool
     */
    public function random_verification_trigger() {
        // 随机触发验证（1/20的概率）
        if (rand(1, 20) === 1) {
            return $this->comprehensive_security_check();
        }
        return true;
    }
    
    /**
     * 获取安全检查结果
     * @return array
     */
    public function get_security_status() {
        return $this->security_checks;
    }
}

/**
 * 全局安全检查函数
 * @return bool
 */
function advanced_security_check() {
    static $checked = false;
    
    if ($checked) {
        return true;
    }
    
    $security = SecurityCore::getInstance();
    $result = $security->comprehensive_security_check();
    
    $checked = true;
    return $result;
}

/**
 * 随机安全验证
 * @return bool
 */
function random_security_verify() {
    $security = SecurityCore::getInstance();
    return $security->random_verification_trigger();
}

?>
