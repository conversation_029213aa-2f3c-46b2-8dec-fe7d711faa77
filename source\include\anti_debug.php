<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统反调试和反破解模块
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

if (!defined('IN_HANFOX')) {
    exit('Access Denied');
}

/**
 * 反调试和反破解类
 */
class AntiDebug {
    
    private $debug_detected = false;
    private $tamper_detected = false;
    
    /**
     * 执行所有反调试检查
     * @return bool
     */
    public function check_all() {
        // 检查调试器
        if ($this->detect_debugger()) {
            $this->debug_detected = true;
            return false;
        }
        
        // 检查虚拟机环境
        if ($this->detect_vm_environment()) {
            $this->debug_detected = true;
            return false;
        }
        
        // 检查常见破解工具
        if ($this->detect_crack_tools()) {
            $this->debug_detected = true;
            return false;
        }
        
        // 检查运行时间异常
        if ($this->detect_timing_attack()) {
            $this->debug_detected = true;
            return false;
        }
        
        return true;
    }
    
    /**
     * 检测调试器
     * @return bool
     */
    private function detect_debugger() {
        // 检查Xdebug
        if (extension_loaded('xdebug')) {
            return true;
        }
        
        // 检查调试相关的环境变量
        $debug_vars = array(
            'XDEBUG_SESSION',
            'XDEBUG_SESSION_START',
            'PHPSTORM_DEBUG',
            'NETBEANS_XDEBUG',
            'ECLIPSE_DBGP'
        );
        
        foreach ($debug_vars as $var) {
            if (isset($_GET[$var]) || isset($_POST[$var]) || isset($_COOKIE[$var])) {
                return true;
            }
        }
        
        // 检查调试端口
        if (function_exists('fsockopen')) {
            $debug_ports = array(9000, 9001, 9003);
            foreach ($debug_ports as $port) {
                $connection = @fsockopen('127.0.0.1', $port, $errno, $errstr, 1);
                if ($connection) {
                    fclose($connection);
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检测虚拟机环境
     * @return bool
     */
    private function detect_vm_environment() {
        // 检查常见虚拟机特征
        $vm_indicators = array();
        
        // 检查服务器软件
        if (isset($_SERVER['SERVER_SOFTWARE'])) {
            $server_software = strtolower($_SERVER['SERVER_SOFTWARE']);
            $vm_patterns = array('vmware', 'virtualbox', 'qemu', 'kvm', 'hyper-v');
            
            foreach ($vm_patterns as $pattern) {
                if (strpos($server_software, $pattern) !== false) {
                    return true;
                }
            }
        }
        
        // 检查主机名
        if (isset($_SERVER['SERVER_NAME'])) {
            $hostname = strtolower($_SERVER['SERVER_NAME']);
            $vm_hostnames = array('sandbox', 'vm', 'virtual', 'test', 'debug');
            
            foreach ($vm_hostnames as $vm_hostname) {
                if (strpos($hostname, $vm_hostname) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检测常见破解工具
     * @return bool
     */
    private function detect_crack_tools() {
        // 检查User-Agent中的破解工具特征
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $user_agent = strtolower($_SERVER['HTTP_USER_AGENT']);
            $crack_tools = array(
                'burp', 'fiddler', 'charles', 'wireshark', 
                'postman', 'insomnia', 'curl', 'wget',
                'python-requests', 'httpie'
            );
            
            foreach ($crack_tools as $tool) {
                if (strpos($user_agent, $tool) !== false) {
                    return true;
                }
            }
        }
        
        // 检查可疑的HTTP头
        $suspicious_headers = array(
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_X_ORIGINATING_IP',
            'HTTP_CLIENT_IP'
        );
        
        foreach ($suspicious_headers as $header) {
            if (isset($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                // 检查是否为本地IP或保留IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检测时间攻击
     * @return bool
     */
    private function detect_timing_attack() {
        static $last_check_time = 0;
        static $check_count = 0;
        
        $current_time = microtime(true);
        
        if ($last_check_time > 0) {
            $time_diff = $current_time - $last_check_time;
            
            // 如果两次检查间隔过短，可能是自动化攻击
            if ($time_diff < 0.1) {
                $check_count++;
                if ($check_count > 10) {
                    return true;
                }
            } else {
                $check_count = 0;
            }
        }
        
        $last_check_time = $current_time;
        return false;
    }
    
    /**
     * 代码混淆检查
     * @return bool
     */
    public function check_code_integrity() {
        // 检查关键函数是否被重定义
        $critical_functions = array(
            'file_get_contents',
            'file_put_contents',
            'fopen',
            'fwrite',
            'include',
            'require'
        );
        
        foreach ($critical_functions as $func) {
            if (!function_exists($func)) {
                return false;
            }
        }
        
        // 检查关键常量
        $critical_constants = array(
            'ROOT_PATH',
            'APP_PATH',
            'IN_HANFOX'
        );
        
        foreach ($critical_constants as $const) {
            if (!defined($const)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 内存使用检查
     * @return bool
     */
    public function check_memory_usage() {
        if (function_exists('memory_get_usage')) {
            $memory_usage = memory_get_usage(true);
            $memory_limit = ini_get('memory_limit');
            
            // 转换内存限制为字节
            $memory_limit_bytes = $this->convert_to_bytes($memory_limit);
            
            // 如果内存使用超过限制的80%，可能存在异常
            if ($memory_usage > $memory_limit_bytes * 0.8) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 转换内存大小为字节
     * @param string $size
     * @return int
     */
    private function convert_to_bytes($size) {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;
        
        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }
        
        return $size;
    }
    
    /**
     * 执行反破解响应
     */
    public function anti_crack_response() {
        // 清除输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        // 发送错误响应
        http_response_code(500);
        header('Content-Type: text/html; charset=utf-8');
        
        // 显示假的错误信息
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统错误</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .error-container { max-width: 600px; margin: 0 auto; background: #fff; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-title { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
        .error-message { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-title">系统维护中</div>
        <div class="error-message">
            <p>系统正在进行例行维护，暂时无法访问。</p>
            <p>预计维护时间：2-4小时</p>
            <p>给您带来的不便，敬请谅解。</p>
        </div>
    </div>
</body>
</html>';
        
        exit;
    }
    
    /**
     * 是否检测到调试
     * @return bool
     */
    public function is_debug_detected() {
        return $this->debug_detected;
    }
    
    /**
     * 是否检测到篡改
     * @return bool
     */
    public function is_tamper_detected() {
        return $this->tamper_detected;
    }
}

/**
 * 全局反调试检查函数
 * @return bool
 */
function anti_debug_check() {
    static $checked = false;
    static $result = true;
    
    if ($checked) {
        return $result;
    }
    
    $anti_debug = new AntiDebug();
    
    // 执行检查
    if (!$anti_debug->check_all()) {
        $anti_debug->anti_crack_response();
        return false;
    }
    
    // 检查代码完整性
    if (!$anti_debug->check_code_integrity()) {
        $anti_debug->anti_crack_response();
        return false;
    }
    
    $checked = true;
    return $result;
}

?>
