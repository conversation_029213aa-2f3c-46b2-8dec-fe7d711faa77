<?php
/* Smarty version 4.5.5, created on 2025-08-02 00:32:35
  from '/www/wwwroot/www.95dir.com/themes/system/option.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_688cec236d6ff0_39156540',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9aadc09abdf10c1f3488807f32d3ca1c189d05f6' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/option.html',
      1 => 1752079122,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_688cec236d6ff0_39156540 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em></h3>
    <div class="formbox">
        <form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<?php if ($_smarty_tpl->tpl_vars['option']->value == 'basic') {?>
			<tr>
				<th>网站名称：</th>
				<td><input name="cfg[site_name]" type="text" class="ipt" id="site_name" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_name'];?>
" /></td>
			</tr>
			<tr>
				<th>网站标题：</th>
				<td><input name="cfg[site_title]" type="text" class="ipt" id="site_title" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_title'];?>
" /></td>
			</tr>
			<tr>
				<th>网站地址：</th>
				<td><input name="cfg[site_url]" type="text" class="ipt" id="site_url" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_url'];?>
" />
				<span class="tips">格式 http://www.yzdir.com/</span></td>
			</tr>
			<tr>
				<th>安装目录：</th>
				<td><input name="cfg[site_root]" type="text" class="ipt" id="site_root" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_root'];?>
" readonly /><span class="tips">系统自动获得正确的路径，仅需手工保存。</span></td>
			</tr>
			<tr>
				<th>站长邮件：</th>
				<td><input name="cfg[admin_email]" type="text" class="ipt" id="admin_email" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['admin_email'];?>
" /><span class="tips">用来发送邮件的邮箱地址</span></td>
			</tr>
			<tr>
				<th valign="top">关 键 词：</th>
				<td><textarea name="cfg[site_keywords]" cols="50" rows="6" class="ipt" id="site_keywords"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_keywords'];?>
</textarea></td>
			</tr>
			<tr>
				<th valign="top">网站描述：</th>
				<td><textarea name="cfg[site_description]" cols="50" rows="6" class="ipt" id="site_description"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_description'];?>
</textarea></td>
			</tr>
			<tr>
				<th valign="top">页底版权：</th>
				<td><textarea name="cfg[site_copyright]" cols="50" rows="6" class="ipt" id="site_copyright"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_copyright'];?>
</textarea></td>
			</tr>
        	<?php } elseif ($_smarty_tpl->tpl_vars['option']->value == 'misc') {?>
			<tr>
				<th>是否启用Gzip压缩功能：</th>
				<td><input name="cfg[is_enabled_gzip]" type="radio" id="is_enabled_gzip_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_gzip'],'yes');?>
><label for="is_enabled_gzip_y">开启</label>　<input name="cfg[is_enabled_gzip]" type="radio" id="is_enabled_gzip_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_gzip'],'no');?>
><label for="is_enabled_gzip_n">关闭</label></td>
			</tr>
			<tr>
				<th>是否启用站点提交功能：</th>
				<td><input name="cfg[is_enabled_submit]" type="radio" id="is_enabled_submit_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_submit'],'yes');?>
 onclick="$('#submit_reason').hide();"><label for="is_enabled_submit_y">开启</label>　<input name="cfg[is_enabled_submit]" type="radio" id="is_enabled_submit_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_submit'],'no');?>
 onclick="$('#submit_reason').show();"><label for="is_enabled_submit_n">关闭</label></td>
			</tr>
			<tr id="submit_reason" style="display: <?php echo opt_display($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_submit'],'no');?>
;">
				<th valign="top">关闭站点提交功能说明：</th>
				<td><textarea name="cfg[submit_close_reason]" cols="50" rows="6" class="ipt" id="submit_close_reason"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['submit_close_reason'];?>
</textarea></td>
			</tr>
			<tr>
				<th>是否开启链接检测功能：</th>
				<td><input name="cfg[is_enabled_linkcheck]" type="radio" id="is_enabled_linkcheck_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_linkcheck'],'yes');?>
 onclick="$('#link_check').show();"><label for="is_enabled_linkcheck_y">开启</label>　<input name="cfg[is_enabled_linkcheck]" type="radio" id="is_enabled_linkcheck_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_linkcheck'],'no');?>
 onclick="$('#link_check').hide();"><label for="is_enabled_linkcheck_n">关闭</label></td>
			</tr>
            <tbody id="link_check" style="display: <?php echo opt_display($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_linkcheck'],'yes');?>
;">
			<tr>
				<th>目标网站所链接的名称：</th>
				<td><input name="cfg[check_link_name]" type="text" class="ipt" id="check_link_name" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['check_link_name'];?>
" /><span class="tips">要检查的链接名称</span></td>
			</tr>
			<tr>
				<th>目标网站所链接的地址：</th>
				<td><input name="cfg[check_link_url]" type="text" class="ipt" id="check_link_url" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['check_link_url'];?>
" /><span class="tips">要检查的链接地址，只需输入域名</span></td>
			</tr>
            </tbody>
			<tr>
				<th>站点数据更新周期设置：</th>
				<td><input name="cfg[data_update_cycle]" type="text" class="ipt" id="data_update_cycle" size="10" maxlength="2" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['data_update_cycle'];?>
" /> 天<span class="tips">系统自动更新PR, SPR, Alexa, 链接检测等</span></td>
			</tr>
			<tr>
				<th>文章中允许的链接数量：</th>
				<td><input name="cfg[article_link_num]" type="text" class="ipt" id="article_link_num" size="10" maxlength="2" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['article_link_num'];?>
" /><span class="tips">发布文章时，文章中允许的链接数量，默认为3</span></td>
			</tr>
            <tr>
				<th>图片文件保存目录设置：</th>
				<td><input name="cfg[upload_dir]" type="text" class="ipt" id="upload_dir" size="10" maxlength="10" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['upload_dir'];?>
" /><span class="tips">只需填写存放图片的目录名称即可，用于存放站点及文章图片</span></td>
			</tr>
            <tr>
            	<th>热门搜索关键词设置：</th>
                <td><input name="cfg[search_words]" type="text" class="ipt" id="search_words" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['search_words'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /><span class="tips">多个词之间用英文的“,”逗号隔开</span></td>
            </tr>
            <tr>
            	<th>AI生成简介API地址：</th>
                <td><input name="cfg[ai_api_url]" type="text" class="ipt" id="ai_api_url" size="80" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['ai_api_url'];?>
" /><span class="tips">智谱AI API端点地址，如：https://open.bigmodel.cn/api/paas/v4/chat/completions</span></td>
            </tr>
            <tr>
            	<th>AI生成简介API密钥：</th>
                <td><input name="cfg[ai_api_key]" type="text" class="ipt" id="ai_api_key" size="80" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['ai_api_key'];?>
" /><span class="tips">智谱AI API密钥，用于身份验证</span></td>
            </tr>
            <tr>
            	<th>AI生成简介模型名称：</th>
                <td><input name="cfg[ai_model_name]" type="text" class="ipt" id="ai_model_name" size="30" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['ai_model_name'];?>
" /><span class="tips">AI模型名称，如：glm-4</span></td>
            </tr>
            <tr>
            	<th>AI生成简介温度参数：</th>
                <td><input name="cfg[ai_temperature]" type="text" class="ipt" id="ai_temperature" size="10" maxlength="5" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['ai_temperature'];?>
" /><span class="tips">控制生成内容的创造性，范围0-1，推荐0.7</span></td>
            </tr>
            <tr>
            	<th valign="top">AI生成简介提示词：</th>
                <td>
                    <textarea name="cfg[ai_prompt_template]" cols="80" rows="8" class="ipt" id="ai_prompt_template"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['ai_prompt_template'];?>
</textarea>
                    <br>
                    <div style="margin-top: 10px;">
                        <button type="button" class="ai-prompt-btn" onclick="showQuickSelector()" style="background: #007cba; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; font-size: 13px; transition: all 0.2s ease;">
                            🎯 快速选择模板
                        </button>
                        <button type="button" class="ai-prompt-btn" onclick="openPromptManager()" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; font-size: 13px; transition: all 0.2s ease;">
                            📋 管理所有模板
                        </button>
                        <button type="button" class="ai-prompt-btn" onclick="openQuickSetup()" style="background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; transition: all 0.2s ease;">
                            ⚙️ AI功能设置
                        </button>
                    </div>
                    <span class="tips">AI生成简介的基础提示词，系统会自动在此基础上添加网站域名、标签和简介信息。可以调整生成风格、字数要求、格式要求等。点击上方按钮可快速选择专业模板。</span>
                </td>
            </tr>
			<tr>
				<th>新站审核微信通知：</th>
				<td><input name="cfg[wechat_robot]" type="text" class="ipt" id="wechat_robot" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['wechat_robot'];?>
" /><span class="tips">企业微信群机器人链接，用于微信通知。如：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=49cfa71e-0d9a-4ec2-****-57941eab4d8d</span></td>
			</tr>
			<tr>
				<th>新站审核钉钉通知：</th>
				<td><input name="cfg[dingding_robot]" type="text" class="ipt" id="dingding_robot" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['dingding_robot'];?>
" /><span class="tips">钉钉群机器人链接，关键词“审核”。如：https://oapi.dingtalk.com/robot/send?access_token=6a6b****5dea8df41ca</span></td>
			</tr>
            <tr>
            	<th>非法关键词过滤设置：</th>
                <td><textarea name="cfg[filter_words]" cols="50" rows="6" class="ipt" id="filter_words" onBlur="javascript:this.value=this.value.replace(/，/ig,',');"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['filter_words'];?>
</textarea><span class="tips">多个词之间用英文的“,”逗号隔开</span></td>
            </tr>
            <tr>
            	<th>快速发布网站公告信息：</th>
                <td><textarea name="cfg[site_notice]" cols="50" rows="6" class="ipt" id="site_notice"><?php echo $_smarty_tpl->tpl_vars['cfg']->value['site_notice'];?>
</textarea></td>
            </tr>
            <?php } elseif ($_smarty_tpl->tpl_vars['option']->value == 'user') {?>
			<tr>
				<th>是否允许新用户注册：</th>
				<td><input name="cfg[is_enabled_register]" type="radio" id="is_enabled_register_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_register'],'yes');?>
><label for="is_enabled_register_y">允许</label>　<input name="cfg[is_enabled_register]" type="radio" id="is_enabled_register_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_register'],'no');?>
><label for="is_enabled_register_n">禁止</label><span class="tips">禁止后用户将无法注册</span></td>
			</tr>
			<tr>
				<th>新用户注册验证：</th>
				<td><input name="cfg[register_email_verify]" type="radio" id="register_email_verify_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['register_email_verify'],'no');?>
><label for="register_email_verify_n">无</label>　<input name="cfg[register_email_verify]" type="radio" id="register_email_verify_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['register_email_verify'],'yes');?>
><label for="register_email_verify_y">Email验证</label><span class="tips">“Email验证”将向用户注册Email发送一封验证邮件以确认邮箱的有效性；</span></td>
			</tr>
			<tr>
				<th>是否启用QQ一键登录：</th>
				<td><input name="cfg[is_enabled_connect]" type="radio" id="is_enabled_connect_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_connect'],'yes');?>
><label for="is_enabled_connect_y">允许</label>　<input name="cfg[is_enabled_connect]" type="radio" id="is_enabled_connect_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_enabled_connect'],'no');?>
><label for="is_enabled_connect_n">禁止</label><span class="tips">启用后将允许用户使用QQ登录或注册</span></td>
			</tr>
			<tr>
				<th>是否允许用户昵称重复：</th>
				<td><input name="cfg[is_nickname_repetition]" type="radio" id="is_nickname_repetition_y" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_nickname_repetition'],'yes');?>
><label for="is_nickname_repetition_y">允许</label>　<input name="cfg[is_nickname_repetition]" type="radio" id="is_nickname_repetition_n" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['is_nickname_repetition'],'no');?>
><label for="is_nickname_repetition_n">禁止</label><span class="tips">禁止将会要求重新输入</span></td>
			</tr>
            <tr>
				<th>QQ一键登录APP ID：</th>
				<td><input name="cfg[qq_appid]" type="text" class="ipt" id="qq_appid" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['qq_appid'];?>
" /><span class="tips">申请地址：http://connect.qq.com/</span></td>
			</tr>
            <tr>
				<th>QQ一键登录APP KEY：</th>
				<td><input name="cfg[qq_appkey]" type="text" class="ipt" id="qq_appkey" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['qq_appkey'];?>
" /><span class="tips">同上</span></td>
			</tr>
        	<?php } elseif ($_smarty_tpl->tpl_vars['option']->value == 'link') {?>
            <tr>
            	<th>网站URL链接结构设置：</th>
                <td><input name="cfg[link_struct]" type="radio" id="link_struct_0" value="0"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['link_struct'],0);?>
><label for="link_struct_0">默认 - http://www.domain.com/?mod=category</label><br /><input name="cfg[link_struct]" type="radio" id="link_struct_1" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['link_struct'],1);?>
><label for="link_struct_1">文件型 - http://www.domain.com/category-1-2.html</label><br /><input name="cfg[link_struct]" type="radio" id="link_struct_2" value="2"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['link_struct'],2);?>
><label for="link_struct_2">目录和文件型 - http://www.domain.com/category/1-2.html</label><br /><input name="cfg[link_struct]" type="radio" id="link_struct_3" value="3"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['link_struct'],3);?>
><label for="link_struct_3">目录型 - http://www.domain.com/category/1/2</label></td>
            </tr>
        	<?php } elseif ($_smarty_tpl->tpl_vars['option']->value == 'mail') {?>
			<tr>
				<th>SMTP服务器地址：</th>
				<td><input name="cfg[smtp_host]" type="text" class="ipt" id="smtp_host" size="50" maxlength="50" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['smtp_host'];?>
" /><span class="tips">发送邮件的服务器地址，例smtp.126.com</span></td>
			</tr>
			<tr>
				<th>SMTP服务器端口：</th>
				<td><input name="cfg[smtp_port]" type="text" class="ipt" id="smtp_port" size="10" maxlength="10" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['smtp_port'];?>
" /><span class="tips">默认端口为25</span></td>
			</tr>
			<tr>
				<th>是否启用SMTP验证功能：</th>
				<td><input name="cfg[smtp_auth]" type="radio" id="smtp_auth1" value="yes"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['smtp_auth'],'yes');?>
><label for="smtp_auth1">开启</label>　<input name="cfg[smtp_auth]" type="radio" id="smtp_auth0" value="no"<?php echo opt_checked($_smarty_tpl->tpl_vars['cfg']->value['smtp_auth'],'no');?>
><label for="smtp_auth0">关闭</label><span class="tips">通常需要开启</span></td>
			</tr>
			<tr>
				<th>SMTP服务器帐号：</th>
				<td><input name="cfg[smtp_user]" type="text" class="ipt" id="smtp_user" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['smtp_user'];?>
" /><span class="tips">登录邮件服务器的帐号</span></td>
			</tr>
			<tr>
				<th>SMTP服务器密码：</th>
				<td><input name="cfg[smtp_pass]" type="password" class="ipt" id="smtp_pass" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['cfg']->value['smtp_pass'];?>
" /><span class="tips">登录邮件服务器的密码</span></td>
			</tr>
        	<?php }?>
			<tr class="btnbox">
            	<th>&nbsp;</th>
				<td>
					<input name="act" type="hidden" id="act" value="update">
					<input type="submit" class="btn" value="保 存">
				</td>
			</tr>
		</table>
		</form>
	</div>

<!-- 快速提示词选择器弹窗 -->
<div id="quickSelectorModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 20px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #f0f0f0; padding-bottom: 15px;">
            <h3 style="margin: 0; color: #333;">🎯 快速选择提示词模板</h3>
            <button onclick="hideQuickSelector()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
        </div>
        <div id="promptTemplateList">
            <div style="text-align: center; padding: 20px; color: #666;">
                正在加载模板列表...
            </div>
        </div>
    </div>
</div>

<style>
/* AI提示词按钮样式 */
.ai-prompt-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    opacity: 0.9;
}

.ai-prompt-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

/* 提示词文本框样式优化 */
#ai_prompt_template {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
    border: 2px solid #ddd;
    border-radius: 6px;
    padding: 10px;
}

#ai_prompt_template:focus {
    border-color: #007cba;
    box-shadow: 0 0 8px rgba(0, 124, 186, 0.2);
    outline: none;
}
</style>

<?php echo '<script'; ?>
>
// AI提示词管理相关函数

// 显示快速选择器
function showQuickSelector() {
    document.getElementById('quickSelectorModal').style.display = 'block';
    loadPromptTemplates();
}

// 隐藏快速选择器
function hideQuickSelector() {
    document.getElementById('quickSelectorModal').style.display = 'none';
}

// 加载提示词模板列表
function loadPromptTemplates() {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', 'quick_prompt_selector.php?action=get_prompts', true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.status === 'success') {
                        displayPromptTemplates(response.prompts);
                        if (response.debug) {
                            console.log('成功调试信息:', response.debug);
                        }
                    } else {
                        console.log('错误调试信息:', response.debug);
                        var errorMsg = '加载失败：' + (response.message || '未知错误');
                        if (response.debug) {
                            errorMsg += '<br><small>调试信息已输出到控制台</small>';
                        }
                        document.getElementById('promptTemplateList').innerHTML =
                            '<div style="text-align: center; padding: 20px; color: #dc3545;">' + errorMsg + '</div>';
                    }
                } catch (e) {
                    console.log('JSON解析错误:', e);
                    console.log('原始响应:', xhr.responseText);
                    document.getElementById('promptTemplateList').innerHTML =
                        '<div style="text-align: center; padding: 20px; color: #dc3545;">响应解析失败，请重试</div>';
                }
            } else {
                console.log('HTTP错误:', xhr.status, xhr.statusText);
                console.log('响应内容:', xhr.responseText);
                document.getElementById('promptTemplateList').innerHTML =
                    '<div style="text-align: center; padding: 20px; color: #dc3545;">请求失败(' + xhr.status + ')<br>请查看浏览器控制台获取详细信息</div>';
            }
        }
    };
    xhr.send();
}

// 显示提示词模板列表
function displayPromptTemplates(prompts) {
    var html = '';
    prompts.forEach(function(prompt) {
        html += '<div style="border: 1px solid #ddd; border-radius: 6px; padding: 15px; margin-bottom: 15px; background: #f9f9f9;">';
        html += '<h4 style="margin: 0 0 8px 0; color: #007cba;">' + prompt.name + '</h4>';
        html += '<p style="margin: 0 0 10px 0; color: #666; font-style: italic;">' + prompt.description + '</p>';
        html += '<div style="background: white; padding: 8px; border-radius: 4px; font-size: 12px; color: #555; margin-bottom: 10px; max-height: 60px; overflow: hidden;">';
        html += prompt.preview;
        html += '</div>';
        html += '<button onclick="applyPromptTemplate(\'' + prompt.key + '\', \'' + prompt.name + '\')" ';
        html += 'style="background: #007cba; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">';
        html += '✅ 应用此模板</button>';
        html += '</div>';
    });
    document.getElementById('promptTemplateList').innerHTML = html;
}

// 应用提示词模板
function applyPromptTemplate(promptKey, promptName) {
    if (!confirm('确定要应用 "' + promptName + '" 模板吗？这将替换当前的提示词内容。')) {
        return;
    }

    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'quick_prompt_selector.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.status === 'success') {
                        // 更新文本框内容
                        document.getElementById('ai_prompt_template').value = response.content;
                        // 显示成功消息
                        alert('✅ ' + response.message);
                        // 隐藏选择器
                        hideQuickSelector();
                    } else {
                        alert('❌ ' + (response.message || '应用失败，请重试'));
                    }
                } catch (e) {
                    console.log('JSON解析错误:', e);
                    console.log('原始响应:', xhr.responseText);
                    alert('❌ 应用失败，响应格式错误');
                }
            } else {
                console.log('HTTP错误:', xhr.status, xhr.statusText);
                alert('❌ 请求失败(' + xhr.status + ')，请重试');
            }
        }
    };
    xhr.send('action=apply_prompt&prompt_key=' + encodeURIComponent(promptKey));
}

function openPromptManager() {
    // 在新窗口中打开提示词管理器
    var width = 1200;
    var height = 800;
    var left = (screen.width - width) / 2;
    var top = (screen.height - height) / 2;

    var promptWindow = window.open(
        'ai_prompt_manager.php',
        'promptManager',
        'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + ',scrollbars=yes,resizable=yes'
    );

    // 监听子窗口关闭事件，刷新当前页面以更新提示词
    var checkClosed = setInterval(function() {
        if (promptWindow.closed) {
            clearInterval(checkClosed);
            // 询问是否刷新页面以加载新的提示词
            if (confirm('提示词可能已更新，是否刷新页面以查看最新配置？')) {
                location.reload();
            }
        }
    }, 1000);

    promptWindow.focus();
}

function openQuickSetup() {
    // 在新窗口中打开快速设置页面
    var width = 900;
    var height = 700;
    var left = (screen.width - width) / 2;
    var top = (screen.height - height) / 2;

    var setupWindow = window.open(
        'quick_prompt_setup.php',
        'quickSetup',
        'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + ',scrollbars=yes,resizable=yes'
    );

    setupWindow.focus();
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideQuickSelector();
    }
});

// 点击弹窗外部关闭
document.getElementById('quickSelectorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideQuickSelector();
    }
});
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
