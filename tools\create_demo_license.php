<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 创建演示授权文件
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

// 获取当前域名
$current_domain = '';
if (isset($_SERVER['HTTP_HOST'])) {
    $current_domain = $_SERVER['HTTP_HOST'];
} elseif (isset($_SERVER['SERVER_NAME'])) {
    $current_domain = $_SERVER['SERVER_NAME'];
}

// 移除端口号
$current_domain = preg_replace('/:\d+$/', '', $current_domain);
$current_domain = strtolower($current_domain);

// 如果没有域名，使用默认值
if (empty($current_domain)) {
    $current_domain = 'localhost';
}

// 创建演示授权信息
$license_info = array(
    'domain' => $current_domain,
    'expire_time' => time() + (365 * 24 * 60 * 60), // 1年后到期
    'version' => '95DIR-v3.0',
    'license_type' => 'trial',
    'customer_name' => '演示用户',
    'customer_email' => '<EMAIL>',
    'machine_code' => '',
    'generate_time' => time(),
    'generator' => 'DemoLicenseCreator v1.0'
);

// 生成签名
$license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';

// 计算签名
$sign_data = $license_info;
ksort($sign_data);
$sign_string = '';
foreach ($sign_data as $key => $value) {
    $sign_string .= $key . '=' . $value . '&';
}
$sign_string = rtrim($sign_string, '&');
$license_info['signature'] = md5($sign_string . $license_key);

// 转换为JSON并加密
$json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);
$encrypted_data = authcode($json_data, 'ENCODE', $license_key);

// 保存授权文件
$license_file = ROOT_PATH . 'data/license.dat';

// 确保目录存在
$data_dir = ROOT_PATH . 'data/';
if (!is_dir($data_dir)) {
    mkdir($data_dir, 0755, true);
}

// 保存文件
$result = file_put_contents($license_file, $encrypted_data);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>创建演示授权文件</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .result { padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">创建演示授权文件</div>
            <p>为当前域名创建一个演示授权文件</p>
        </div>
        
        <?php if ($result !== false): ?>
        <div class="result success">
            <h3>✓ 演示授权文件创建成功！</h3>
            <p>授权文件已保存到：<?php echo htmlspecialchars($license_file); ?></p>
            <p>文件大小：<?php echo strlen($encrypted_data); ?> 字节</p>
        </div>
        <?php else: ?>
        <div class="result error">
            <h3>✗ 演示授权文件创建失败！</h3>
            <p>无法写入授权文件，请检查目录权限。</p>
        </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>授权信息</h3>
            <div class="info-item">
                <span class="info-label">授权域名：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['domain']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">到期时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['expire_time']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权版本：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['version']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权类型：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['license_type']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">客户信息：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['customer_name']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">生成时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['generate_time']); ?></span>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="license_test.php" class="btn">测试授权</a>
            <a href="license_generator.php" class="btn">生成正式授权</a>
            <a href="../index.php" class="btn">返回首页</a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>说明：</h4>
            <ol>
                <li>此演示授权文件仅用于测试目的</li>
                <li>授权有效期为1年</li>
                <li>授权类型为试用版</li>
                <li>正式使用请通过授权生成工具创建正式授权</li>
            </ol>
        </div>
    </div>
</body>
</html>
