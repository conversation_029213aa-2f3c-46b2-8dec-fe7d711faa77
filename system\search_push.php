<?php
require('common.php');

$fileurl = 'search_push.php';
$tempfile = 'search_push.html';

if (!isset($action)) $action = 'config';

// 获取推送配置函数
function get_push_config() {
    global $DB;

    $config = array();
    $table = $DB->table('options');

    $baidu_token = $DB->get_one("SELECT option_value FROM $table WHERE option_name='baidu_push_token'");
    $google_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='google_api_key'");
    $bing_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='bing_api_key'");

    $config['baidu_token'] = $baidu_token ? $baidu_token['option_value'] : '';
    $config['google_key'] = $google_key ? $google_key['option_value'] : '';
    $config['bing_key'] = $bing_key ? $bing_key['option_value'] : '';

    return $config;
}

// 保存推送配置函数
function save_push_config($baidu_token, $google_key, $bing_key) {
    global $DB;

    $table = $DB->table('options');

    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('baidu_push_token', '$baidu_token')");
    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('google_api_key', '$google_key')");
    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('bing_api_key', '$bing_key')");

    return true;
}

/** 配置页面 */
if ($action == 'config') {
    $pagetitle = '搜索引擎推送配置';

    // 处理配置保存
    if ($_POST['submit']) {
        $baidu_token = trim($_POST['baidu_token']);
        $google_key = trim($_POST['google_key']);
        $bing_key = trim($_POST['bing_key']);

        if (save_push_config($baidu_token, $google_key, $bing_key)) {
            msgbox('配置保存成功！', $fileurl);
        } else {
            msgbox('配置保存失败！');
        }
    }

    // 获取当前配置
    $config = get_push_config();

    $smarty->assign('config', $config);
}



smarty_output($tempfile);
?>
