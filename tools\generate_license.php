<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 简化的授权文件生成工具
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

/**
 * 简化的授权文件生成器
 */
function generate_license_file($domain, $expire_time, $version = '95DIR-v3.0', $license_type = 'developer') {
    // 授权信息
    $license_info = array(
        'domain' => $domain,
        'expire_time' => $expire_time,
        'version' => $version,
        'license_type' => $license_type,
        'customer_name' => '95DIR测试用户',
        'customer_email' => '<EMAIL>',
        'machine_code' => '7e217d6d470d761543b548bca7549699',
        'generate_time' => time(),
        'generator' => 'SimpleLicenseGenerator v1.0'
    );
    
    // 使用原始的授权密钥
    $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
    
    // 计算签名
    $sign_data = $license_info;
    ksort($sign_data);
    $sign_string = '';
    foreach ($sign_data as $key => $value) {
        $sign_string .= $key . '=' . $value . '&';
    }
    $sign_string = rtrim($sign_string, '&');
    $license_info['signature'] = md5($sign_string . $license_key);
    
    // 转换为JSON
    $json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);
    
    // 使用authcode加密
    $encrypted_data = authcode($json_data, 'ENCODE', $license_key);
    
    return $encrypted_data;
}

// 为www.95dir.com生成授权文件
$domain = 'www.95dir.com';
$expire_time = strtotime('2025-12-31 23:59:59');
$encrypted_data = generate_license_file($domain, $expire_time);

// 保存到data目录
$license_file = ROOT_PATH . 'data/license.dat';
$result = file_put_contents($license_file, $encrypted_data);

if ($result !== false) {
    echo "授权文件生成成功！\n";
    echo "文件路径: " . $license_file . "\n";
    echo "文件大小: " . strlen($encrypted_data) . " 字节\n";
    echo "授权域名: " . $domain . "\n";
    echo "到期时间: " . date('Y-m-d H:i:s', $expire_time) . "\n";
    
    // 验证生成的文件
    echo "\n正在验证生成的授权文件...\n";
    
    $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
    $test_decrypt = authcode($encrypted_data, 'DECODE', $license_key);
    $test_data = json_decode($test_decrypt, true);
    
    if ($test_data && isset($test_data['domain']) && $test_data['domain'] === $domain) {
        echo "✅ 授权文件验证成功！\n";
        echo "解密后的数据:\n";
        print_r($test_data);
    } else {
        echo "❌ 授权文件验证失败！\n";
    }
} else {
    echo "❌ 授权文件生成失败！\n";
}
?>
