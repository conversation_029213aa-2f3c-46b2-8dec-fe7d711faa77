<?php
/* Smarty version 4.5.5, created on 2025-07-30 18:36:18
  from '/www/wwwroot/www.95dir.com/themes/default/datastats.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889f5a2b83d63_55724930',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9e50e4bba2d7e08b1d07a502fb4ccec0c7f6d3f6' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/datastats.html',
      1 => 1753179210,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889f5a2b83d63_55724930 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="数据公示,服务器状态,爬虫统计,<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="查看网站服务器运行状态、系统信息和搜索引擎爬虫访问统计数据" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/datastats.css" rel="stylesheet" type="text/css" />
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
    <?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    
    <div id="subbox" class="clearfix">
        <div class="sitepath"><span style="float: right;"><?php echo get_adcode(1);?>
</span><?php echo $_smarty_tpl->tpl_vars['site_path']->value;?>
</div>
        
        <div class="datastats-container">
            <h1 class="page-title">数据公示 <span class="live-indicator" title="实时更新中"></span></h1>
            
            <!-- 服务器基本信息 -->
            <div class="stats-section">
                <h2 class="section-title">服务器信息</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">当前时间</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['datetime'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">系统运行</span>
                        <span class="value uptime-display" data-start-time="<?php echo (($tmp = $_smarty_tpl->tpl_vars['server_info']->value['start_timestamp'] ?? null)===null||$tmp==='' ? 0 ?? null : $tmp);?>
" data-uptime-seconds="<?php echo (($tmp = $_smarty_tpl->tpl_vars['server_info']->value['uptime_seconds'] ?? null)===null||$tmp==='' ? 0 ?? null : $tmp);?>
"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['uptime_text'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">系统负载</span>
                        <span class="value">
                            <span class="load-value" data-target="<?php echo $_smarty_tpl->tpl_vars['server_info']->value['load_1min'];?>
">0.000</span><br>
                            <span class="load-value" data-target="<?php echo $_smarty_tpl->tpl_vars['server_info']->value['load_5min'];?>
">0.000</span><br>
                            <span class="load-value" data-target="<?php echo $_smarty_tpl->tpl_vars['server_info']->value['load_15min'];?>
">0.000</span>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="label">操作系统</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['os'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">服务器端</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['software'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">PHP版本</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['php_version'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">MySQL版本</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['mysql_version'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Smarty版本</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['smarty_version'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">上传限制</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['upload_limit'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">运行内存</span>
                        <span class="value">
                            <span class="memory-value" data-target="<?php echo $_smarty_tpl->tpl_vars['server_info']->value['memory_usage'];?>
">0M</span>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="label">磁盘空间</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['disk_usage'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">请求超时</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['timeout'];?>
</span>
                    </div>
                    <div class="info-item">
                        <span class="label">联系邮箱</span>
                        <span class="value"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['contact_email'];?>
</span>
                    </div>
                    <div class="info-item" style="grid-column: 1 / -1;">
                        <span class="label">环境状态</span>
                        <span class="value" style="font-size: 12px; color: #7f8c8d;"><?php echo $_smarty_tpl->tpl_vars['server_info']->value['env_notes'];?>
</span>
                    </div>
                </div>
            </div>
            
            <!-- 今日访问统计 -->
            <div class="stats-section">
                <h2 class="section-title">今日访问统计 <span class="live-indicator" title="每30秒自动更新"></span></h2>
                <div class="today-stats">
                    <div class="stat-item">
                        <span class="stat-label">今日总浏览</span>
                        <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['today_stats']->value['total_visits'];?>
</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日站点浏览</span>
                        <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['today_stats']->value['site_visits'];?>
</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日文章浏览</span>
                        <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['today_stats']->value['article_visits'];?>
</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日出站次数</span>
                        <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['today_stats']->value['outlinks'];?>
</span>
                    </div>
                </div>
            </div>
            
            <!-- 爬虫访问统计表格 -->
            <div class="stats-section">
                <h2 class="section-title">搜索引擎爬虫访问统计</h2>
                <p class="spider-subtitle">Diligent and Adorable little Spider</p>
                
                <div class="table-container">
                    <table class="spider-table">
                        <thead>
                            <tr>
                                <th>Day</th>
                                <th>Google 爬</th>
                                <th>Baidu 爬</th>
                                <th>Bing 爬</th>
                                <th>Yandex 爬</th>
                                <th>Sogou 爬</th>
                                <th>360</th>
                                <th>Byte 爬</th>
                                <th>Yahoo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['spider_stats']->value, 'stat');
$_smarty_tpl->tpl_vars['stat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['stat']->value) {
$_smarty_tpl->tpl_vars['stat']->do_else = false;
?>
                            <tr>
                                <td><strong><?php echo $_smarty_tpl->tpl_vars['stat']->value['date'];?>
</strong></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['google'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['baidu'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['bing'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['yandex'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['sogou'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['so360'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['bytedance'];?>
">0</span></td>
                                <td><span class="spider-count" data-target="<?php echo $_smarty_tpl->tpl_vars['stat']->value['yahoo'];?>
">0</span></td>
                            </tr>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 网站数据统计 -->
            <div class="stats-section">
                <h2 class="section-title">网站数据统计</h2>
                <div class="site-stats">
                    <div class="stat-group">
                        <div class="stat-item" data-stat="category">
                            <span class="stat-label">网站分类</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['category'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="website">
                            <span class="stat-label">收录网站</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['website'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="article">
                            <span class="stat-label">发布文章</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['article'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="apply">
                            <span class="stat-label">待审网站</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['apply'];?>
</span>
                        </div>
                    </div>
                    <div class="stat-group">
                        <div class="stat-item" data-stat="vip">
                            <span class="stat-label">VIP网站</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['vip'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="recommend">
                            <span class="stat-label">推荐网站</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['recommend'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="user">
                            <span class="stat-label">注册用户</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['user'];?>
</span>
                        </div>
                        <div class="stat-item" data-stat="link">
                            <span class="stat-label">友情链接</span>
                            <span class="stat-value"><?php echo $_smarty_tpl->tpl_vars['site_stats']->value['link'];?>
</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>

<?php echo '<script'; ?>
>
// 实时更新数据
function updateRealTimeData() {
    // 更新当前时间
    updateCurrentTime();

    // 更新系统运行时间
    updateUptime();

    // 更新系统负载（模拟波动）
    updateSystemLoad();
}

// 更新当前时间
function updateCurrentTime() {
    const now = new Date();
    const timeStr = now.getFullYear() + '年' +
                   String(now.getMonth() + 1).padStart(2, '0') + '月' +
                   String(now.getDate()).padStart(2, '0') + '日 ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0');

    // 查找当前时间显示元素
    const infoItems = document.querySelectorAll('.info-item');
    infoItems.forEach(item => {
        const label = item.querySelector('.label');
        if (label && label.textContent.includes('当前时间')) {
            const valueElement = item.querySelector('.value');
            if (valueElement) {
                valueElement.textContent = timeStr;
            }
        }
    });
}

// 更新系统运行时间（基于服务器启动时间精确计算）
function updateUptime() {
    const uptimeElement = document.querySelector('.uptime-display');
    if (uptimeElement) {
        const startTimestamp = parseInt(uptimeElement.getAttribute('data-start-time'));

        if (startTimestamp > 0) {
            // 基于服务器启动时间精确计算当前运行时间
            const currentTime = Math.floor(Date.now() / 1000);
            const uptimeSeconds = currentTime - startTimestamp;

            const days = Math.floor(uptimeSeconds / 86400);
            const hours = Math.floor((uptimeSeconds % 86400) / 3600);
            const minutes = Math.floor((uptimeSeconds % 3600) / 60);
            const seconds = Math.floor(uptimeSeconds % 60);

            const newText = `${days} 天 ${hours} 小时 ${minutes} 分 ${seconds} 秒`;

            // 只有当显示的秒数发生变化时才更新
            if (uptimeElement.textContent !== newText) {
                uptimeElement.textContent = newText;

                // 添加轻微的更新效果
                uptimeElement.style.color = '#3498db';
                setTimeout(() => {
                    uptimeElement.style.color = '';
                }, 200);
            }
        } else {
            // 如果没有启动时间，使用简单的递增方式
            const text = uptimeElement.textContent;
            const matches = text.match(/(\d+) 天 (\d+) 小时 (\d+) 分 (\d+) 秒/);
            if (matches) {
                let days = parseInt(matches[1]);
                let hours = parseInt(matches[2]);
                let minutes = parseInt(matches[3]);
                let seconds = parseInt(matches[4]) + 1;

                if (seconds >= 60) {
                    seconds = 0;
                    minutes++;
                    if (minutes >= 60) {
                        minutes = 0;
                        hours++;
                        if (hours >= 24) {
                            hours = 0;
                            days++;
                        }
                    }
                }

                uptimeElement.textContent = `${days} 天 ${hours} 小时 ${minutes} 分 ${seconds} 秒`;
            }
        }
    }
}

// 更新今日统计数据
function updateTodayStats() {
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach((element, index) => {
        const currentValue = parseInt(element.textContent.replace(/,/g, ''));
        if (!isNaN(currentValue)) {
            // 随机小幅增长
            const increment = Math.floor(Math.random() * 3) + 1;
            const newValue = currentValue + increment;
            element.textContent = newValue.toLocaleString();

            // 添加闪烁效果
            element.style.color = '#27ae60';
            setTimeout(() => {
                element.style.color = '';
            }, 1000);
        }
    });
}

// 更新系统负载（实时波动）
function updateSystemLoad() {
    const loadValues = document.querySelectorAll('.load-value');
    loadValues.forEach((element, index) => {
        const currentValue = parseFloat(element.textContent);
        if (!isNaN(currentValue)) {
            // 基于时间的波动，让负载看起来真实
            const time = Date.now() / 1000;
            const baseVariation = Math.sin(time / 60 + index) * 0.1; // 基础波动
            const randomVariation = (Math.random() - 0.5) * 0.05; // 随机波动

            let newValue = currentValue + baseVariation + randomVariation;

            // 确保负载值在合理范围内
            newValue = Math.max(0, Math.min(2.0, newValue));

            element.textContent = newValue.toFixed(3);

            // 根据负载值改变颜色
            if (newValue > 1.0) {
                element.style.color = '#e74c3c'; // 高负载红色
            } else if (newValue > 0.7) {
                element.style.color = '#f39c12'; // 中等负载橙色
            } else {
                element.style.color = '#27ae60'; // 低负载绿色
            }
        }
    });
}

// 页面加载完成后开始数字滚动动画
document.addEventListener('DOMContentLoaded', function() {
    // 表格行悬停效果
    const rows = document.querySelectorAll('.spider-table tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f0f8ff';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // 启动数字滚动动画
    setTimeout(() => {
        startNumberAnimations();
    }, 500);

    // 开始实时更新
    updateRealTimeData();

    // 每秒更新时间和运行时间
    setInterval(function() {
        updateCurrentTime();
        updateUptime();
    }, 1000);

    // 每5秒更新系统负载
    setInterval(updateSystemLoad, 5000);

    // 每10秒检查爬虫数据更新（爬虫访问比较频繁）
    setInterval(checkForRealUpdates, 10000);

    // 每30秒检查网站数据更新
    setInterval(checkForRealUpdates, 30000);

    // 添加页面标题动态效果
    let titleCounter = 0;
    setInterval(function() {
        titleCounter++;
        document.title = '数据公示 (' + titleCounter + ') - 95分类目录';
    }, 60000);
});

// 数字滚动动画函数
function animateNumber(element, targetValue, duration = 2000) {
    const startValue = 0;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数让动画更自然
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

        // 格式化数字显示
        element.textContent = currentValue.toLocaleString();

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = targetValue.toLocaleString();
        }
    }

    requestAnimationFrame(updateNumber);
}

// 浮点数滚动动画函数（用于系统负载）
function animateFloat(element, targetValue, duration = 2000) {
    const startValue = 0;
    const startTime = performance.now();

    element.style.color = '#3498db';
    element.style.textShadow = '0 0 10px rgba(52, 152, 219, 0.5)';

    function updateFloat(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = startValue + (targetValue - startValue) * easeOutQuart;

        element.textContent = currentValue.toFixed(3);

        if (progress < 1) {
            requestAnimationFrame(updateFloat);
        } else {
            element.textContent = targetValue.toFixed(3);

            element.style.color = '#27ae60';
            element.style.textShadow = '0 0 15px rgba(39, 174, 96, 0.8)';

            setTimeout(() => {
                element.style.color = '';
                element.style.textShadow = '';
            }, 1000);
        }
    }

    requestAnimationFrame(updateFloat);
}

// 内存使用滚动动画函数
function animateMemory(element, targetValue, targetText, duration = 2000) {
    const startValue = 0;
    const startTime = performance.now();

    element.style.color = '#3498db';
    element.style.textShadow = '0 0 10px rgba(52, 152, 219, 0.5)';

    function updateMemory(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

        element.textContent = currentValue + 'M';

        if (progress < 1) {
            requestAnimationFrame(updateMemory);
        } else {
            element.textContent = targetText;

            element.style.color = '#27ae60';
            element.style.textShadow = '0 0 15px rgba(39, 174, 96, 0.8)';

            setTimeout(() => {
                element.style.color = '';
                element.style.textShadow = '';
            }, 1000);
        }
    }

    requestAnimationFrame(updateMemory);
}

// 页面加载时启动数字滚动动画
function startNumberAnimations() {
    let animationDelay = 0;

    // 1. 为今日访问统计添加滚动效果
    const todayStatValues = document.querySelectorAll('.today-stats .stat-value');
    todayStatValues.forEach((element, index) => {
        const targetValue = parseInt(element.textContent.replace(/,/g, ''));
        if (!isNaN(targetValue) && targetValue > 0) {
            element.textContent = '0';
            setTimeout(() => {
                animateNumber(element, targetValue, 2500);
            }, animationDelay);
            animationDelay += 200;
        }
    });

    // 2. 为系统负载添加滚动效果
    const loadValues = document.querySelectorAll('.load-value');
    loadValues.forEach((element, index) => {
        const targetValue = parseFloat(element.getAttribute('data-target'));
        if (!isNaN(targetValue) && targetValue > 0) {
            element.textContent = '0.000';
            setTimeout(() => {
                animateFloat(element, targetValue, 2000);
            }, animationDelay);
            animationDelay += 150;
        }
    });

    // 3. 为内存使用添加滚动效果
    const memoryValues = document.querySelectorAll('.memory-value');
    memoryValues.forEach((element, index) => {
        const targetText = element.getAttribute('data-target');
        const targetValue = parseInt(targetText.replace(/[^\d]/g, ''));
        if (!isNaN(targetValue) && targetValue > 0) {
            element.textContent = '0M';
            setTimeout(() => {
                animateMemory(element, targetValue, targetText, 2000);
            }, animationDelay);
            animationDelay += 200;
        }
    });

    // 4. 为爬虫统计表格添加滚动效果
    const spiderCounts = document.querySelectorAll('.spider-count');
    spiderCounts.forEach((element, index) => {
        const targetValue = parseInt(element.getAttribute('data-target'));
        if (!isNaN(targetValue) && targetValue >= 0) {
            element.textContent = '0';
            setTimeout(() => {
                animateNumber(element, targetValue, 1500);
            }, animationDelay + Math.floor(index / 8) * 300);
            animationDelay += 50;
        }
    });

    // 5. 为网站数据统计添加滚动效果
    const siteStatValues = document.querySelectorAll('.site-stats .stat-value');
    siteStatValues.forEach((element, index) => {
        const targetValue = parseInt(element.textContent.replace(/,/g, ''));
        if (!isNaN(targetValue) && targetValue > 0) {
            element.textContent = '0';
            setTimeout(() => {
                animateNumber(element, targetValue, 3000);
            }, animationDelay);
            animationDelay += 200;
        }
    });
}

// 实时更新数字（只有数据真正变化时才更新）
function checkForRealUpdates() {
    // 获取当前显示的网站统计数值
    const currentSiteStats = {};
    document.querySelectorAll('.site-stats .stat-item').forEach((item, index) => {
        const label = item.querySelector('.stat-label').textContent;
        const value = parseInt(item.querySelector('.stat-value').textContent.replace(/,/g, ''));
        currentSiteStats[label] = value;
    });

    // 获取当前显示的今日爬虫统计数值（表格第一行）
    const currentSpiderStats = {};
    const firstRow = document.querySelector('.spider-table tbody tr:first-child');
    if (firstRow) {
        const spiderCounts = firstRow.querySelectorAll('.spider-count');
        const spiderNames = ['google', 'baidu', 'bing', 'yandex', 'sogou', 'so360', 'bytedance', 'yahoo'];
        spiderCounts.forEach((element, index) => {
            if (spiderNames[index]) {
                currentSpiderStats[spiderNames[index]] = parseInt(element.textContent.replace(/,/g, ''));
            }
        });
    }

    // 通过AJAX检查数据库中的最新数值
    fetch('?mod=datastats&ajax=1')
        .then(response => response.json())
        .then(data => {
            // 检查网站统计数据更新
            Object.keys(data.site_stats).forEach(key => {
                const newValue = data.site_stats[key];
                const element = document.querySelector(`[data-stat="${key}"] .stat-value`);
                if (element) {
                    const currentValue = parseInt(element.textContent.replace(/,/g, ''));
                    if (newValue !== currentValue && newValue > currentValue) {
                        // 网站数据增加了，播放滚动动画
                        animateNumber(element, newValue, 1500);

                        // 添加闪烁提示
                        element.parentElement.style.boxShadow = '0 0 20px rgba(39, 174, 96, 0.6)';
                        setTimeout(() => {
                            element.parentElement.style.boxShadow = '';
                        }, 2000);
                    }
                }
            });

            // 检查爬虫统计数据更新
            if (data.spider_stats && firstRow) {
                const spiderCounts = firstRow.querySelectorAll('.spider-count');
                const spiderNames = ['google', 'baidu', 'bing', 'yandex', 'sogou', 'so360', 'bytedance', 'yahoo'];

                spiderCounts.forEach((element, index) => {
                    if (spiderNames[index] && data.spider_stats[spiderNames[index]] !== undefined) {
                        const newValue = data.spider_stats[spiderNames[index]];
                        const currentValue = parseInt(element.textContent.replace(/,/g, ''));

                        if (newValue !== currentValue && newValue > currentValue) {
                            // 爬虫数据增加了，播放滚动动画
                            animateNumber(element, newValue, 1000);

                            // 添加特殊的爬虫更新效果
                            element.style.backgroundColor = '#3498db';
                            element.style.color = 'white';
                            element.style.borderRadius = '3px';
                            element.style.padding = '2px 4px';

                            setTimeout(() => {
                                element.style.backgroundColor = '';
                                element.style.color = '';
                                element.style.borderRadius = '';
                                element.style.padding = '';
                            }, 2000);

                            // 在控制台显示爬虫更新信息
                            console.log(`🕷️ ${spiderNames[index]} 爬虫访问增加: ${currentValue} → ${newValue}`);
                        }
                    }
                });
            }
        })
        .catch(error => {
            console.log('检查更新失败:', error);
        });
}
<?php echo '</script'; ?>
>

</body>
</html>
<?php }
}
