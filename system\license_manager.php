<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统授权管理后台
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

require('common.php');

$pagetitle = '授权管理';
$tempfile = 'license_manager.html';

// 检查是否为超级管理员
if ($myself['user_id'] != 1) {
    msgbox('只有超级管理员才能访问授权管理！', './main.php');
}

// 加载授权验证模块
require(APP_PATH . 'include/license.php');

$action = $_GET['act'] ?? $_POST['act'] ?? 'status';

// 授权状态检查
if ($action == 'status') {
    $license_file = ROOT_PATH . 'data/license.dat';
    $license_exists = file_exists($license_file);
    $license_info = array();
    $license_valid = false;
    
    if ($license_exists) {
        $validator = new LicenseValidator();
        $license_valid = $validator->validate();
        
        if (!$license_valid) {
            $error_message = $validator->get_error_message();
        }
        
        // 尝试读取授权文件详细信息
        try {
            $encrypted_content = file_get_contents($license_file);
            $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
            $decrypted_content = authcode($encrypted_content, 'DECODE', $license_key);
            
            if ($decrypted_content) {
                $license_info = json_decode($decrypted_content, true);
            }
        } catch (Exception $e) {
            // 忽略错误
        }
    }
    
    // 获取系统信息
    $system_info = array(
        'domain' => $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'unknown',
        'version' => defined('SYS_VERSION') ? SYS_VERSION : 'unknown',
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown'
    );
    
    // 生成机器码
    $validator = new LicenseValidator();
    $machine_code = $validator->generate_machine_code();
    
    $smarty->assign('license_exists', $license_exists);
    $smarty->assign('license_valid', $license_valid);
    $smarty->assign('license_info', $license_info);
    $smarty->assign('system_info', $system_info);
    $smarty->assign('machine_code', $machine_code);
    $smarty->assign('error_message', $error_message ?? '');
}

// 上传授权文件
if ($action == 'upload') {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_FILES['license_file']) && $_FILES['license_file']['error'] === UPLOAD_ERR_OK) {
            $upload_file = $_FILES['license_file']['tmp_name'];
            $license_file = ROOT_PATH . 'data/license.dat';
            
            // 验证上传的文件
            $file_content = file_get_contents($upload_file);
            $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
            $decrypted_content = authcode($file_content, 'DECODE', $license_key);
            
            if ($decrypted_content && json_decode($decrypted_content, true)) {
                // 文件有效，保存
                if (move_uploaded_file($upload_file, $license_file)) {
                    msgbox('授权文件上传成功！', './license_manager.php');
                } else {
                    msgbox('授权文件保存失败！');
                }
            } else {
                msgbox('无效的授权文件！');
            }
        } else {
            msgbox('请选择要上传的授权文件！');
        }
    }
}

// 删除授权文件
if ($action == 'delete') {
    $license_file = ROOT_PATH . 'data/license.dat';
    if (file_exists($license_file)) {
        if (unlink($license_file)) {
            msgbox('授权文件删除成功！', './license_manager.php');
        } else {
            msgbox('授权文件删除失败！');
        }
    } else {
        msgbox('授权文件不存在！');
    }
}

// 在线验证
if ($action == 'online_verify') {
    $result = online_license_verify();
    if ($result) {
        msgbox('在线验证成功！', './license_manager.php');
    } else {
        msgbox('在线验证失败！请检查网络连接或联系技术支持。');
    }
}

// 文件完整性检查
if ($action == 'integrity_check') {
    $result = check_file_integrity();
    if ($result) {
        msgbox('文件完整性检查通过！', './license_manager.php');
    } else {
        msgbox('文件完整性检查失败！系统文件可能被篡改。');
    }
}

// 生成机器码信息
if ($action == 'machine_info') {
    $validator = new LicenseValidator();
    $machine_code = $validator->generate_machine_code();
    
    $machine_info = array(
        'machine_code' => $machine_code,
        'domain' => $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'unknown',
        'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
        'system_version' => defined('SYS_VERSION') ? SYS_VERSION : 'unknown'
    );
    
    $smarty->assign('machine_info', $machine_info);
}

smarty_output($tempfile);
?>
