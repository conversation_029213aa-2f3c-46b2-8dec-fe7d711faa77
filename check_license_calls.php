<?php
/*
 * <AUTHOR> 95DIR系统
 * @Description  : 检查系统中的授权验证调用
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

echo "<h1>检查授权验证调用</h1>";

// 定义要检查的文件
$files_to_check = array(
    'index.php',
    'source/init.php',
    'source/include/license.php',
    'system/common.php'
);

// 要搜索的授权相关函数
$license_functions = array(
    'verify_system_license',
    'quick_license_check',
    'advanced_security_check',
    'random_security_verify',
    'integrity_guard_check'
);

echo "<h2>文件检查结果：</h2>";

foreach ($files_to_check as $file) {
    echo "<h3>检查文件：$file</h3>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $lines = explode("\n", $content);
        
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px;'>";
        
        $found_calls = false;
        foreach ($license_functions as $func) {
            foreach ($lines as $line_num => $line) {
                if (strpos($line, $func) !== false) {
                    $found_calls = true;
                    $line_display = $line_num + 1;
                    $is_commented = (trim($line)[0] ?? '') === '/' && (trim($line)[1] ?? '') === '/';
                    $status = $is_commented ? '✅ 已注释' : '❌ 未注释';
                    
                    echo "<strong>第{$line_display}行</strong> - $status: <code>" . htmlspecialchars(trim($line)) . "</code><br>";
                }
            }
        }
        
        if (!$found_calls) {
            echo "✅ 未发现授权验证调用<br>";
        }
        
        echo "</div>";
    } else {
        echo "<div style='color: red;'>❌ 文件不存在</div>";
    }
}

echo "<h2>修复建议：</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
echo "<p>如果发现有<strong>未注释</strong>的授权验证调用，请在相应文件中添加 <code>//</code> 注释符号。</p>";
echo "<p>例如：</p>";
echo "<code>if (!verify_system_license()) {</code><br>";
echo "改为：<br>";
echo "<code>// if (!verify_system_license()) {</code>";
echo "</div>";

echo "<h2>快速修复脚本：</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 4px;'>";
echo "<p>将以下内容保存为 <code>disable_license.php</code> 并在测试站运行：</p>";
echo "<textarea style='width: 100%; height: 200px; font-family: monospace;'>";
echo htmlspecialchars('<?php
// 禁用授权验证的快速脚本
$files_to_fix = array(
    "source/init.php",
    "index.php", 
    "system/common.php"
);

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // 注释掉授权验证调用
        $content = preg_replace("/^(\s*)(if\s*\(\s*!\s*verify_system_license\(\s*\)\s*\)\s*\{)/m", "$1// $2", $content);
        $content = preg_replace("/^(\s*)(if\s*\(\s*!\s*advanced_security_check\(\s*\)\s*\)\s*\{)/m", "$1// $2", $content);
        $content = preg_replace("/^(\s*)(if\s*\(\s*!\s*random_security_verify\(\s*\)\s*\)\s*\{)/m", "$1// $2", $content);
        $content = preg_replace("/^(\s*)(if\s*\(\s*!\s*integrity_guard_check\(\s*\)\s*\)\s*\{)/m", "$1// $2", $content);
        
        file_put_contents($file, $content);
        echo "已修复文件: $file\n";
    }
}

echo "修复完成！请刷新网站测试。\n";
?>');
echo "</textarea>";
echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2, h3 { color: #333; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
