<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 显示授权文件内容，用于手动复制
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

// 为test.95dir.com创建授权信息
$license_info = array(
    'domain' => 'test.95dir.com',
    'expire_time' => strtotime('2025-12-31 23:59:59'),
    'version' => '95DIR-v3.0',
    'license_type' => 'developer',
    'customer_name' => '95DIR测试用户',
    'customer_email' => '<EMAIL>',
    'machine_code' => '7e217d6d470d761543b548bca7549699',
    'generate_time' => time(),
    'generator' => 'ManualLicenseCreator v1.0'
);

// 使用原始的授权密钥
$license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';

// 计算签名
$sign_data = $license_info;
ksort($sign_data);
$sign_string = '';
foreach ($sign_data as $key => $value) {
    $sign_string .= $key . '=' . $value . '&';
}
$sign_string = rtrim($sign_string, '&');
$license_info['signature'] = md5($sign_string . $license_key);

// 转换为JSON
$json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);

// 使用authcode加密
$encrypted_data = authcode($json_data, 'ENCODE', $license_key);

// 验证解密
$test_decrypt = authcode($encrypted_data, 'DECODE', $license_key);
$test_data = json_decode($test_decrypt, true);
$verify_ok = ($test_data && isset($test_data['domain']) && $test_data['domain'] === 'test.95dir.com');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>授权文件内容 - 手动复制</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .content-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #dee2e6; }
        .license-content { background: #fff; border: 2px solid #007bff; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all; line-height: 1.4; max-height: 300px; overflow-y: auto; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .steps { background: #fff3cd; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #ffeaa7; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">授权文件内容</div>
            <p>为 test.95dir.com 生成的授权文件内容</p>
        </div>
        
        <?php if ($verify_ok): ?>
        <div class="alert alert-success">
            <h4>✅ 授权文件生成并验证成功！</h4>
            <p>文件内容已生成，验证通过，可以安全使用。</p>
        </div>
        <?php else: ?>
        <div class="alert alert-warning">
            <h4>⚠️ 验证警告</h4>
            <p>授权文件生成完成，但验证可能有问题，请仔细检查。</p>
        </div>
        <?php endif; ?>
        
        <div class="content-box">
            <h3>📋 授权信息</h3>
            <div class="info-item">
                <span class="info-label">授权域名：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['domain']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">绑定机器码：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['machine_code']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">到期时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['expire_time']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权版本：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['version']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">文件大小：</span>
                <span class="info-value"><?php echo strlen($encrypted_data); ?> 字节</span>
            </div>
        </div>
        
        <div class="content-box">
            <h3>📄 授权文件内容（请完整复制）</h3>
            <p><strong>说明：</strong>请选择下面框中的所有内容，复制并保存为 <code>license.dat</code> 文件</p>
            
            <div class="license-content" id="licenseContent"><?php echo htmlspecialchars($encrypted_data); ?></div>
            
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn btn-success" onclick="copyLicenseContent()">📋 复制授权内容</button>
                <button class="btn" onclick="selectAllContent()">🔍 全选内容</button>
            </div>
        </div>
        
        <div class="steps">
            <h4>🚀 手动部署步骤：</h4>
            <ol>
                <li><strong>复制内容</strong>：点击"复制授权内容"按钮，或手动选择上面框中的所有内容</li>
                <li><strong>创建文件</strong>：在您的电脑上创建一个新的文本文件</li>
                <li><strong>粘贴内容</strong>：将复制的内容粘贴到文本文件中</li>
                <li><strong>保存文件</strong>：将文件保存为 <code>license.dat</code>（注意扩展名）</li>
                <li><strong>上传文件</strong>：将 <code>license.dat</code> 上传到测试站的 <code>data</code> 目录</li>
                <li><strong>检查路径</strong>：确保文件路径为 <code>test.95dir.com/data/license.dat</code></li>
                <li><strong>设置权限</strong>：将文件权限设置为 644</li>
                <li><strong>测试访问</strong>：访问 <code>https://test.95dir.com</code> 检查是否正常</li>
            </ol>
        </div>
        
        <div class="content-box">
            <h4>⚠️ 重要注意事项：</h4>
            <ul>
                <li><strong>完整复制</strong>：必须复制完整的内容，不能有遗漏</li>
                <li><strong>文件名</strong>：必须命名为 <code>license.dat</code></li>
                <li><strong>文件位置</strong>：必须放在 <code>data</code> 目录下</li>
                <li><strong>文件格式</strong>：保存为纯文本格式，不要用Word等软件</li>
                <li><strong>字符编码</strong>：使用UTF-8编码保存</li>
                <li><strong>文件大小</strong>：上传后检查文件大小是否为 <?php echo strlen($encrypted_data); ?> 字节</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="../index.php" class="btn">返回主站</a>
        </div>
    </div>
    
    <script>
        function copyLicenseContent() {
            const content = document.getElementById('licenseContent').textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(content).then(function() {
                    alert('授权文件内容已复制到剪贴板！\n\n请将内容保存为 license.dat 文件，然后上传到 test.95dir.com/data/ 目录。');
                }).catch(function(err) {
                    console.error('复制失败: ', err);
                    selectAllContent();
                    alert('自动复制失败，请手动选择并复制内容。');
                });
            } else {
                selectAllContent();
                alert('请手动选择并复制上面框中的所有内容。');
            }
        }
        
        function selectAllContent() {
            const element = document.getElementById('licenseContent');
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }
    </script>
</body>
</html>
