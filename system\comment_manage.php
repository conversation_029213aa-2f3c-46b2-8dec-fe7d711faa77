<?php
require('common.php');

$fileurl = 'comment_manage.php';
$tempfile = 'comment_manage.html';
$pagetitle = '评论管理';

// 获取操作类型
$action = isset($_GET['action']) ? trim($_GET['action']) : 'list';

// 分页参数
$curpage = max(1, intval($_GET['page']));
$pagesize = 20;
$start = ($curpage - 1) * $pagesize;

// 搜索参数
$keywords = isset($_GET['keywords']) ? trim($_GET['keywords']) : '';
$web_id = isset($_GET['web_id']) ? intval($_GET['web_id']) : 0;
$status = isset($_GET['status']) ? intval($_GET['status']) : -1;

// 构建查询条件
$where_conditions = array();
$where_params = array();

if (!empty($keywords)) {
    $where_conditions[] = "(c.comment_content LIKE ? OR w.web_name LIKE ?)";
    $where_params[] = "%$keywords%";
    $where_params[] = "%$keywords%";
}

if ($web_id > 0) {
    $where_conditions[] = "c.web_id = ?";
    $where_params[] = $web_id;
}

if ($status >= 0) {
    $where_conditions[] = "c.status = ?";
    $where_params[] = $status;
}

$where_sql = empty($where_conditions) ? '1=1' : implode(' AND ', $where_conditions);

if ($action == 'list') {
    // 获取评论列表
    $sql = "SELECT c.*, w.web_name, u.nick_name as user_name 
            FROM " . $DB->table('website_comments') . " c 
            LEFT JOIN " . $DB->table('websites') . " w ON c.web_id = w.web_id 
            LEFT JOIN " . $DB->table('users') . " u ON c.user_id = u.user_id 
            WHERE $where_sql 
            ORDER BY c.create_time DESC 
            LIMIT $start, $pagesize";
    
    $comments = $DB->fetch_all($sql);
    
    // 处理评论数据
    foreach ($comments as &$comment) {
        $comment['display_time'] = date('Y-m-d H:i:s', $comment['create_time']);
        $comment['display_name'] = $comment['user_id'] > 0 ? 
            ($comment['user_name'] ? $comment['user_name'] : '会员用户') : '匿名';
        
        // 检测违规内容
        $comment['is_violation'] = check_comment_violation($comment['comment_content']);
        
        // 截取评论内容用于列表显示
        $comment['short_content'] = mb_strlen($comment['comment_content'], 'utf-8') > 50 ? 
            mb_substr($comment['comment_content'], 0, 50, 'utf-8') . '...' : 
            $comment['comment_content'];
    }
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) as total 
                  FROM " . $DB->table('website_comments') . " c 
                  LEFT JOIN " . $DB->table('websites') . " w ON c.web_id = w.web_id 
                  WHERE $where_sql";
    $total_result = $DB->fetch_one($count_sql);
    $total = $total_result['total'];
    
    // 分页
    $pageurl = $fileurl . '?action=list';
    if (!empty($keywords)) $pageurl .= '&keywords=' . urlencode($keywords);
    if ($web_id > 0) $pageurl .= '&web_id=' . $web_id;
    if ($status >= 0) $pageurl .= '&status=' . $status;
    
    $showpage = showpage($pageurl, $total, $curpage, $pagesize);
    
    // 获取统计信息
    $stats = array(
        'total' => $DB->get_count($DB->table('website_comments'), '1=1'),
        'normal' => $DB->get_count($DB->table('website_comments'), 'status=1'),
        'hidden' => $DB->get_count($DB->table('website_comments'), 'status=0'),
        'violation' => 0 // 这个需要通过PHP检测计算
    );
    
    $smarty->assign('comments', $comments);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('stats', $stats);
    $smarty->assign('keywords', $keywords);
    $smarty->assign('web_id', $web_id);
    $smarty->assign('status', $status);
}

// 删除评论
if ($action == 'delete') {
    $comment_ids = (array) ($_POST['comment_id'] ? $_POST['comment_id'] : $_GET['comment_id']);
    
    if (!empty($comment_ids)) {
        $ids = implode(',', array_map('intval', $comment_ids));
        // 软删除：将状态设为0，同时删除相关回复
        $DB->query("UPDATE " . $DB->table('website_comments') . " 
                   SET status = 0 
                   WHERE comment_id IN ($ids) OR parent_id IN ($ids)");
        
        msgbox('评论删除成功！', $fileurl);
    } else {
        msgbox('请选择要删除的评论！');
    }
}

// 恢复评论
if ($action == 'restore') {
    $comment_ids = (array) ($_POST['comment_id'] ? $_POST['comment_id'] : $_GET['comment_id']);
    
    if (!empty($comment_ids)) {
        $ids = implode(',', array_map('intval', $comment_ids));
        $DB->query("UPDATE " . $DB->table('website_comments') . " 
                   SET status = 1 
                   WHERE comment_id IN ($ids)");
        
        msgbox('评论恢复成功！', $fileurl);
    } else {
        msgbox('请选择要恢复的评论！');
    }
}

// 批量检测违规
if ($action == 'check_violation') {
    require_once(APP_PATH . 'module/option.php');
    $options = get_options();
    
    $violation_count = 0;
    $comments = $DB->fetch_all("SELECT comment_id, comment_content FROM " . $DB->table('website_comments') . " WHERE status = 1");
    
    foreach ($comments as $comment) {
        if (check_comment_violation($comment['comment_content'])) {
            $violation_count++;
            // 可以选择自动隐藏违规评论
            // $DB->query("UPDATE " . $DB->table('website_comments') . " SET status = 0 WHERE comment_id = " . $comment['comment_id']);
        }
    }
    
    msgbox("检测完成！发现 $violation_count 条违规评论。", $fileurl);
}

/**
 * 检测评论违规内容
 */
function check_comment_violation($content) {
    global $options;
    
    if (empty($options)) {
        require_once(APP_PATH . 'module/option.php');
        $options = get_options();
    }
    
    // 使用系统的违规词检测
    if (!empty($options['filter_words'])) {
        if (!censor_words($options['filter_words'], $content)) {
            return true;
        }
    }
    
    return false;
}

smarty_output($tempfile);
?>
