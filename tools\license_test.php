<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统授权测试工具
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 加载必要的文件
require(APP_PATH . 'include/function.php');
require(APP_PATH . 'version.php');
require(APP_PATH . 'include/license.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR授权测试工具</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .test-result { padding: 15px; margin: 20px 0; border-radius: 4px; }
        .test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR授权测试工具</div>
            <p>用于测试95DIR分类目录系统的授权状态</p>
        </div>
        
        <div class="info-box">
            <h3>系统信息</h3>
            <div class="info-item">
                <span class="info-label">系统版本：</span>
                <span class="info-value"><?php echo defined('SYS_VERSION') ? SYS_VERSION : '未知'; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">当前域名：</span>
                <span class="info-value"><?php 
                    $validator = new LicenseValidator();
                    $domain = '';
                    if (isset($_SERVER['HTTP_HOST'])) {
                        $domain = $_SERVER['HTTP_HOST'];
                    } elseif (isset($_SERVER['SERVER_NAME'])) {
                        $domain = $_SERVER['SERVER_NAME'];
                    }
                    echo htmlspecialchars($domain);
                ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">机器码：</span>
                <span class="info-value"><?php echo $validator->generate_machine_code(); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权文件：</span>
                <span class="info-value">
                    <?php 
                    $license_file = ROOT_PATH . 'data/license.dat';
                    if (file_exists($license_file)) {
                        echo '<span class="status-success">存在</span> (' . date('Y-m-d H:i:s', filemtime($license_file)) . ')';
                    } else {
                        echo '<span class="status-error">不存在</span>';
                    }
                    ?>
                </span>
            </div>
        </div>
        
        <?php
        // 执行授权验证测试
        $test_results = array();
        
        // 测试1：授权文件存在性
        $license_file = ROOT_PATH . 'data/license.dat';
        $test_results['file_exists'] = array(
            'name' => '授权文件存在性检查',
            'status' => file_exists($license_file),
            'message' => file_exists($license_file) ? '授权文件存在' : '授权文件不存在'
        );
        
        // 测试2：授权验证
        if (file_exists($license_file)) {
            $validator = new LicenseValidator();
            $license_valid = $validator->validate();
            $test_results['license_valid'] = array(
                'name' => '授权验证',
                'status' => $license_valid,
                'message' => $license_valid ? '授权验证通过' : $validator->get_error_message()
            );
            
            // 如果验证失败，尝试获取授权文件详细信息
            if (!$license_valid) {
                try {
                    $encrypted_content = file_get_contents($license_file);
                    $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
                    $decrypted_content = authcode($encrypted_content, 'DECODE', $license_key);
                    
                    if ($decrypted_content) {
                        $license_data = json_decode($decrypted_content, true);
                        if ($license_data) {
                            echo '<div class="info-box">';
                            echo '<h3>授权文件详细信息</h3>';
                            echo '<div class="info-item"><span class="info-label">授权域名：</span><span class="info-value">' . htmlspecialchars($license_data['domain'] ?? '未知') . '</span></div>';
                            echo '<div class="info-item"><span class="info-label">到期时间：</span><span class="info-value">' . (isset($license_data['expire_time']) ? date('Y-m-d H:i:s', $license_data['expire_time']) : '未知') . '</span></div>';
                            echo '<div class="info-item"><span class="info-label">授权版本：</span><span class="info-value">' . htmlspecialchars($license_data['version'] ?? '未知') . '</span></div>';
                            echo '<div class="info-item"><span class="info-label">授权类型：</span><span class="info-value">' . htmlspecialchars($license_data['license_type'] ?? '未知') . '</span></div>';
                            echo '<div class="info-item"><span class="info-label">客户信息：</span><span class="info-value">' . htmlspecialchars($license_data['customer_name'] ?? '未知') . '</span></div>';
                            echo '</div>';
                        }
                    }
                } catch (Exception $e) {
                    // 忽略错误
                }
            }
        } else {
            $test_results['license_valid'] = array(
                'name' => '授权验证',
                'status' => false,
                'message' => '无法验证：授权文件不存在'
            );
        }
        
        // 显示测试结果
        foreach ($test_results as $test) {
            $class = $test['status'] ? 'test-success' : 'test-error';
            $status_text = $test['status'] ? '✓ 通过' : '✗ 失败';
            
            echo '<div class="test-result ' . $class . '">';
            echo '<strong>' . $test['name'] . '：</strong> ' . $status_text . '<br>';
            echo $test['message'];
            echo '</div>';
        }
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="license_generator.php" class="btn">生成授权文件</a>
            <a href="javascript:location.reload()" class="btn">重新测试</a>
            <?php if (file_exists($license_file)): ?>
            <a href="?action=delete_license" class="btn btn-danger" onclick="return confirm('确定要删除授权文件吗？')">删除授权文件</a>
            <?php endif; ?>
        </div>
        
        <?php
        // 处理删除授权文件
        if (isset($_GET['action']) && $_GET['action'] === 'delete_license') {
            if (file_exists($license_file)) {
                if (unlink($license_file)) {
                    echo '<div class="test-result test-success">授权文件已删除</div>';
                    echo '<script>setTimeout(function(){ location.href = "license_test.php"; }, 2000);</script>';
                } else {
                    echo '<div class="test-result test-error">删除授权文件失败</div>';
                }
            }
        }
        ?>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>测试说明：</h4>
            <ol>
                <li>此工具用于测试系统的授权状态</li>
                <li>如果授权验证失败，请检查授权文件是否正确</li>
                <li>确保域名、版本、到期时间等信息匹配</li>
                <li>如需重新生成授权文件，请使用授权文件生成工具</li>
            </ol>
        </div>
    </div>
</body>
</html>
