<?php
/* Smarty version 4.5.5, created on 2025-07-30 18:54:02
  from '/www/wwwroot/www.95dir.com/themes/default/index.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889f9ca30b7f9_60281814',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9a3cac53d17783b3ad8f94972f0d0673f42495a8' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/index.html',
      1 => 1753872828,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:topbar.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889f9ca30b7f9_60281814 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
    <meta name="keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
    <meta name="description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
    <meta name="author" content="95目录网" />
    <meta name="copyright" content="Powered By 95dir.com" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta name="baiduspider" content="index,follow" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="95目录网 - 优质网站分类目录">
    <meta name="theme-color" content="#007bff">
    <meta name="msapplication-TileColor" content="#007bff">
    <meta name="msapplication-navbutton-color" content="#007bff">

    <!-- SEO优化 - Open Graph标签 -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="<?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
" />
    <meta property="og:description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
    <meta property="og:url" content="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
" />
    <meta property="og:site_name" content="95目录网" />
    <meta property="og:locale" content="zh_CN" />

    <!-- SEO优化 - Twitter Card标签 -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="<?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
" />
    <meta name="twitter:description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />

    <!-- SEO优化 - 结构化数据 -->
    <?php echo '<script'; ?>
 type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "95目录网",
        "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
",
        "description": "<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
",
        "inLanguage": "zh-CN",
        "isFamilyFriendly": true,
        "potentialAction": {
            "@type": "SearchAction",
            "target": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=search&query={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "mainEntity": {
            "@type": "ItemList",
            "name": "优质网站分类目录",
            "description": "收录各行业优质网站的分类目录"
        }
    }
    <?php echo '</script'; ?>
>

    <!-- SEO优化 - 移动端友好性声明 -->
    <?php echo '<script'; ?>
 type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "MobileApplication",
        "name": "95目录网",
        "operatingSystem": "All",
        "applicationCategory": "BusinessApplication",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        }
    }
    <?php echo '</script'; ?>
>

    <!-- SEO优化 - 网站验证 -->
    <meta name="baidu-site-verification" content="codeva-<?php echo md5($_smarty_tpl->tpl_vars['site_url']->value);?>
" />
    <meta name="google-site-verification" content="google-<?php echo md5($_smarty_tpl->tpl_vars['site_url']->value);?>
" />

    <!-- SEO优化 - 规范链接 -->
    <link rel="canonical" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
" />

    <!-- SEO优化 - 网站图标 -->
    <link rel="icon" type="image/x-icon" href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
favicon.ico" />
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
favicon.ico" />
    <link rel="apple-touch-icon" href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
favicon.ico" />

    <!-- SEO优化 - DNS预解析 -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="//t0.gstatic.cn" />
    <link rel="dns-prefetch" href="//www.google.com" />
    <link rel="dns-prefetch" href="//www.baidu.com" />

    <!-- SEO优化 - 预连接重要资源 -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin />
    <link rel="preconnect" href="https://t0.gstatic.cn" crossorigin />

    <!-- ========== 样式表 ========= -->
    <link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/nav.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/footer-modern.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
          integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- ========== 脚本 ========= -->
    <?php echo '<script'; ?>
 type="text/javascript">
        var sitepath = '<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
';
        var rewrite  = '<?php echo $_smarty_tpl->tpl_vars['cfg']->value['link_struct'];?>
';
    <?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/scripts/jquery.min.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/scripts/common.js?v=2.0"><?php echo '</script'; ?>
>

</head>

<body>
    <!-- SEO优化 - 结构化数据：面包屑导航 -->
    <?php echo '<script'; ?>
 type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
            }
        ]
    }
    <?php echo '</script'; ?>
>

    <!-- SEO优化 - 结构化数据：组织信息 -->
    <?php echo '<script'; ?>
 type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "95目录网",
        "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
",
        "logo": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
logo.png",
        "description": "<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
",
        "sameAs": [
            "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
        ]
    }
    <?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div id="wrapper">
    <!-- ================= 顶部区域（logo / 搜索 / 导航） ================= -->
    <header id="header" role="banner">
        <!-- ---------- LOGO + 搜索 ---------- -->
        <div id="topbox">
            <a href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
" class="logo" title="<?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
" aria-label="95目录网首页"></a>

            <div id="sobox" role="search">
                <form name="sofrm" class="sofrm" method="get" action="" onSubmit="return rewrite_search()" role="search" aria-label="网站搜索">
                    <input name="mod"  type="hidden" value="search" />
                    <input name="type" type="hidden" id="type" value="name" />

                    <div id="selopt">
                        <div id="cursel">网站名称</div>
                        <ul id="options" role="listbox" aria-label="搜索类型选择">
                            <li><a href="javascript: void(0);" name="name" role="option">网站名称</a></li>
                            <li><a href="javascript: void(0);" name="url" role="option">网站地址</a></li>
                            <li><a href="javascript: void(0);" name="tags" role="option">TAG标签</a></li>
                            <li><a href="javascript: void(0);" name="intro" role="option">网站描述</a></li>
                            <li><a href="javascript: void(0);" name="article" role="option">文章关键词</a></li>
                        </ul>
                    </div>

                    <input name="query" type="text" class="sipt" id="query" placeholder="请输入搜索关键词" aria-label="搜索关键词输入框" autocomplete="off" />
                    <input type="submit" class="sbtn" value="搜 索" aria-label="执行搜索" />
                </form>
            </div>
        </div>

        <!-- ---------- 主导航 ---------- -->
        <nav id="navbox" role="navigation" aria-label="主导航菜单">
            <ul class="navbar" role="menubar">
        		<li role="none"><a href="?mod=index" role="menuitem" aria-label="返回网站首页"><i class="fas fa-home" aria-hidden="true"></i> 网站首页</a></li>
                        <li role="none"><a href="?mod=webdir" role="menuitem" aria-label="浏览网站目录"><i class="fas fa-folder" aria-hidden="true"></i> 网站目录</a></li>
                        <li role="none"><a href="?mod=vip_list" class="vip-link" role="menuitem" aria-label="查看VIP站点"><i class="fas fa-crown" aria-hidden="true"></i> VIP站点</a></li>
                        <li role="none"><a href="?mod=article" role="menuitem" aria-label="阅读站长资讯"><i class="fas fa-newspaper" aria-hidden="true"></i> 站长资讯</a></li>
                        <li role="none"><a href="?mod=weblink" role="menuitem" aria-label="友情链接交换"><i class="fas fa-link" aria-hidden="true"></i> 链接交换</a></li>
                        <li role="none"><a href="?mod=category" role="menuitem" aria-label="按分类浏览网站"><i class="fas fa-list" aria-hidden="true"></i> 分类浏览</a></li>
                        <li role="none"><a href="?mod=update" role="menuitem" aria-label="查看最新收录网站"><i class="fas fa-clock" aria-hidden="true"></i> 最新收录</a></li>
                        <li role="none"><a href="?mod=pending" role="menuitem" aria-label="查看待审核网站"><i class="fas fa-hourglass-half" aria-hidden="true"></i> 待审核</a></li>
                        <li role="none"><a href="?mod=archives" role="menuitem" aria-label="查看数据归档"><i class="fas fa-archive" aria-hidden="true"></i> 数据归档</a></li>
                        <li role="none"><a href="?mod=top" role="menuitem" aria-label="查看TOP排行榜"><i class="fas fa-trophy" aria-hidden="true"></i> TOP排行榜</a></li>
                        <li role="none"><a href="?mod=feedback" role="menuitem" aria-label="提交意见反馈"><i class="fas fa-comments" aria-hidden="true"></i> 意见反馈</a></li>
                        <li role="none"><a href="/wailian" role="menuitem" aria-label="外链发布工具"><i class="fas fa-anchor" aria-hidden="true"></i> 外链发布</a></li>
                        <!--<li role="none"><a href="http://my0713.com" role="menuitem"><i class="far fa-play-circle" aria-hidden="true"></i> 免费影视</a></li>-->
        	</ul>
        </nav>

        <!-- ---------- 公告与快捷链接 ---------- -->
        <div id="txtbox">
            <!-- 数据统计 -->
            <div class="count stats-display">
                数据统计：
                <b class="stat-green"><?php echo $_smarty_tpl->tpl_vars['stat']->value['category'];?>
</b>个主题分类，
                <b class="stat-green"><?php echo $_smarty_tpl->tpl_vars['stat']->value['website'];?>
</b>个优秀站点，
                <b class="stat-pink"><?php echo $_smarty_tpl->tpl_vars['stat']->value['vip'];?>
</b>个VIP站点，
                <b class="stat-success"><?php echo $_smarty_tpl->tpl_vars['stat']->value['recommend'];?>
</b>个推荐位，
                <b class="stat-orange"><?php echo $_smarty_tpl->tpl_vars['stat']->value['apply'];?>
</b>个待审站点，
                <b class="stat-warning"><?php echo $_smarty_tpl->tpl_vars['stat']->value['rejected'];?>
</b>个审核不通过，
                <b class="stat-danger"><?php echo $_smarty_tpl->tpl_vars['stat']->value['blacklist'];?>
</b>个黑名单，
                <b class="stat-green"><?php echo $_smarty_tpl->tpl_vars['stat']->value['article'];?>
</b>篇站长资讯，
                <b class="stat-green"><?php echo $_smarty_tpl->tpl_vars['stat']->value['user'];?>
</b>名优质会员
                <!-- SEO优化：链接质量统计 -->
                <?php if ((isset($_smarty_tpl->tpl_vars['stat']->value['linked'])) && (isset($_smarty_tpl->tpl_vars['stat']->value['unlinked']))) {?>
                ，<b class="stat-success"><?php echo $_smarty_tpl->tpl_vars['stat']->value['linked'];?>
</b>个互链站点，
                <b class="stat-muted"><?php echo $_smarty_tpl->tpl_vars['stat']->value['unlinked'];?>
</b>个单向收录
                <?php }?>
            </div>
            <div class="site-notice">
                <ul id="lunbo">
                    <li>
                        <p>
                            一共收录 <b class="stat-green stat-highlight"><?php echo $_smarty_tpl->tpl_vars['stat']->value['website'];?>
</b> 个优秀站点，
                            其中VIP站点 <b class="stat-pink stat-highlight"><?php echo $_smarty_tpl->tpl_vars['stat']->value['vip'];?>
</b> 个，
                            推荐位 <b class="stat-success stat-highlight"><?php echo $_smarty_tpl->tpl_vars['stat']->value['recommend'];?>
</b> 个，
                            注册会员 <b class="stat-green stat-highlight"><?php echo $_smarty_tpl->tpl_vars['stat']->value['user'];?>
</b> 名，
                            <strong>提示：</strong><b class="stat-red">每日更新站点，轻松到首页</b>
                        </p>
                    </li>
                    <li>
                        <p>
                            <span style="color: #ff0000">
                                本网站目录提供网站快速收录服务，
                                <a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">vip席位30元/年（直链）推荐位10元/年（内链）</a>。
                                <strong><a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">我要上推荐</a></strong>
                                联系客服：
                                <a href="tencent://message/?uin=3632094&amp;Site=95目录分类&amp;Menu=yes" target="blank" rel="nofollow">
                                    <img border="0" alt="95目录分类" src="http://wpa.qq.com/pa?p=1:3632094:4">
                                </a>
                            </span>
                        </p>
                    </li>
                </ul>
            </div>

            <div class="link">
                <a href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=quicksubmit" class="quick-submit-link"
                   style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
                          color: white; padding: 6px 12px; border-radius: 15px; text-decoration: none;
                          font-weight: bold; font-size: 12px; margin-right: 15px;
                          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                          transition: all 0.3s ease; display: inline-block;"
                   onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(255, 107, 53, 0.4)';"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(255, 107, 53, 0.3)';">
                    🚀 快速提交
                </a>
                快捷方式：
                <a href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
member/?mod=website&act=add">网站提交</a> -
                <a href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
member/?mod=article&act=add">软文投稿</a> -
                <a href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=diypage&pid=1">帮助中心</a>
            </div>
        </div>
    </header>
<div class="blank10"></div>
    <!-- ================= VIP 推荐区 ================= -->
    <div id="inbox1" class="clearfix" aria-labelledby="vip-section-title">
        <h3 id="vip-section-title">
            <span class="vip-title">
                <img src="/public/images/viptq.png" width="80" height="20" alt="95目录网VIP推荐网站 - 优质网站推荐" title="VIP推荐网站标识" loading="lazy">
            </span>
            <button onclick="showDonatePopup()" class="donate-button main-donate"
                    aria-label="申请推荐位服务">
                我要上推荐 <span class="shine-effect"></span>
            </button>
        </h3>

        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,12,true), 'quick');
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
?>
        <ul class="inlist1">
            <li>
                <!-- 浏览量显示 -->
                <div class="view-count-badge" style="position: absolute; top: 2px; right: 2px; background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; font-size: 10px; font-weight: bold; padding: 2px 6px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 10;">
                    推广 <?php if ($_smarty_tpl->tpl_vars['quick']->value['web_views'] > 0) {
echo $_smarty_tpl->tpl_vars['quick']->value['web_views'];
} else { ?>0<?php }?> 次
                </div>
                <a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" title="VIP：<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
">
                    <img src="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_pic'];?>
" width="110" height="35"
                         alt="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站logo - VIP推荐网站" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站截图预览" class="thumb" loading="lazy" />
                </a><br>
                <a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" title="VIP：<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"
                   class="vip-site-link">
                    <?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>

                    <svg t="1741858436151" class="icon" viewBox="0 0 2048 1024" version="1.1"
                         xmlns="http://www.w3.org/2000/svg" p-id="1496" width="18" height="18">
                        <path d="M128 0h1792c76.8 0 128 51.2 128 128v768c0 76.8-51.2 128-128 128H128C51.2 1024 0 972.8 0 896V128C0 64 51.2 0 128 0z"
                              fill="#FF6600" p-id="1497"></path>
                        <path d="M473.6 832c-25.6 0-51.2-25.6-64-76.8L192 179.2h166.4L512 576l243.2-396.8h179.2l-371.2 576c-38.4 64-64 76.8-89.6 76.8z
                                 m409.6-12.8L972.8 192h153.6l-89.6 627.2zM1856 422.4c-12.8 64-38.4 128-102.4 179.2-51.2 38.4-115.2 64-179.2 64h-268.8L1280
                                 819.2h-153.6l51.2-320h435.2c25.6 0 38.4-12.8 51.2-25.6 12.8-12.8 25.6-38.4 25.6-51.2 0-25.6 0-38.4-12.8-51.2-12.8-12.8
                                 -38.4-25.6-51.2-25.6h-435.2l128-166.4h320c64 0 128 25.6 166.4 64 51.2 64 64 128 51.2 179.2z"
                              fill="#FFFFFF" p-id="1498"></path>
                    </svg>
                </a>
            </li>
        </ul>
        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    </div>

    <!-- 打赏弹窗 -->
    <div id="donate-popup" class="main-popup">
        <div class="donate-popup-content main-content">
            <span class="close main-close" onclick="closeDonatePopup()">&times;</span>
            <h3 class="main-title">
                推荐服务价格表
            </h3>
            <div class="service-info">
                <p>
                    <strong class="price-green">10元上推荐位</strong> - 首页推荐展示<br>
                    <strong class="price-pink">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                    <strong class="price-orange">5元快审服务</strong> - 1-3个工作日审核
                </p>
            </div>
            <p class="note-text">
                备注格式：<strong class="highlight-orange">推荐/vip/快审+网址</strong>
            </p>
            <div class="donate-qr-codes main-qr">
                <div class="qr-item">
                    <h4>微信支付</h4>
                    <img src="/uploads/article/weixin.jpg" alt="95目录网微信支付二维码 - 网站收录付费服务" title="微信扫码支付网站收录费用" class="qr-image" loading="lazy">
                </div>
                <div class="qr-item">
                    <h4>支付宝支付</h4>
                    <img src="/uploads/article/zhifubao.jpg" alt="95目录网支付宝支付二维码 - 网站收录付费服务" title="支付宝扫码支付网站收录费用" class="qr-image" loading="lazy">
                </div>
            </div>
            <div class="service-desc">
                <h4>服务说明：</h4>
                <ul>
                    <li>推荐位：展示在首页推荐区域</li>
                    <li>VIP位：展示在顶部VIP推广区</li>
                    <li>快审：1-3个工作日完成审核</li>
                    <li>付款后请联系客服提供网站信息</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    <!-- ================= 主体：左栏 ================= -->
    <div id="homebox">
        <div id="homebox-left">
            <!-- ========== 实用工具列表 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="tools-title">
                <dt id="tools-title"><a href="/" aria-label="实用工具列表"><i class="fas fa-tools icon-green" aria-hidden="true"></i>实用工具</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="实用工具链接">
                        <li><a href="https://www.12306.cn" target="_blank" rel="nofollow noopener" aria-label="12306火车票预订">火 车 票</a></li>
                        <li><a href="https://tianqi.qq.com" target="_blank" rel="nofollow noopener" aria-label="腾讯天气预报">天气预报</a></li>
                        <li><a href="https://www.rili.com.cn" target="_blank" rel="nofollow noopener" aria-label="万年历查询">万 年 历</a></li>
                        <li><a href="https://flights.ctrip.com/fuzzysearch/search" target="_blank" rel="nofollow noopener" aria-label="携程特价机票">特价机票</a></li>
                        <li><a href="http://typhoon.nmc.cn/mobile.html" target="_blank" rel="nofollow noopener" aria-label="台风路径实时查询">台风路径</a></li>
                        <li><a href="https://www.kuaidi100.com" target="_blank" rel="nofollow noopener" aria-label="快递100查询">快递查询</a></li>
                        <li><a href="https://www.8684.cn" target="_blank" rel="nofollow noopener" aria-label="8684公交线路查询">公交线路</a></li>
                        <li><a href="https://summary.jrj.com.cn/hsMarket" target="_blank" rel="nofollow noopener" aria-label="金融界股票行情">股票行情</a></li>
                        <li><a href="https://dey.11185.cn/web/#/idtoolkitAddress" target="_blank" rel="nofollow noopener" aria-label="邮政编码查询">邮编查询</a></li>
                        <li><a href="https://www.boc.cn/sourcedb/whpj/" target="_blank" rel="nofollow noopener" aria-label="中国银行外汇牌价">外汇牌价</a></li>
                        <li><a href="https://www.cwl.gov.cn/ygkj/wqkjgg/ssq/" target="_blank" rel="nofollow noopener" aria-label="福利彩票开奖结果">福利彩票</a></li>
                        <li><a href="https://bj.122.gov.cn" target="_blank" rel="nofollow noopener" aria-label="交通违章查询">违章查询</a></li>
                        <li><a href="https://map.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度地图导航">百度地图</a></li>
                        <li><a href="https://fanyi.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度在线翻译">在线翻译</a></li>
                        <li><a href="https://www.speedtest.cn" target="_blank" rel="nofollow noopener" aria-label="网络测速工具">网速测试</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 按地区搜索 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="province-tags-title">
                <dt id="province-tags-title">
                    <a href="javascript:void(0);" aria-label="按地区搜索">
                        <i class="fas fa-map-marker-alt icon-green" aria-hidden="true"></i>按地区搜索
                    </a>
                </dt>
                <dd>
                    <ul class="hcatelist province-tags-list" role="list" aria-label="中国省份地区标签">
                        <!-- 直辖市 -->
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="北京" role="button" tabindex="0" aria-label="搜索北京地区网站">北京</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="上海" role="button" tabindex="0" aria-label="搜索上海地区网站">上海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="天津" role="button" tabindex="0" aria-label="搜索天津地区网站">天津</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="重庆" role="button" tabindex="0" aria-label="搜索重庆地区网站">重庆</a></li>

                        <!-- 省份 -->
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河北" role="button" tabindex="0" aria-label="搜索河北地区网站">河北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山西" role="button" tabindex="0" aria-label="搜索山西地区网站">山西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="辽宁" role="button" tabindex="0" aria-label="搜索辽宁地区网站">辽宁</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="吉林" role="button" tabindex="0" aria-label="搜索吉林地区网站">吉林</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="黑龙江" role="button" tabindex="0" aria-label="搜索黑龙江地区网站">黑龙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江苏" role="button" tabindex="0" aria-label="搜索江苏地区网站">江苏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="浙江" role="button" tabindex="0" aria-label="搜索浙江地区网站">浙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="安徽" role="button" tabindex="0" aria-label="搜索安徽地区网站">安徽</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="福建" role="button" tabindex="0" aria-label="搜索福建地区网站">福建</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江西" role="button" tabindex="0" aria-label="搜索江西地区网站">江西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山东" role="button" tabindex="0" aria-label="搜索山东地区网站">山东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河南" role="button" tabindex="0" aria-label="搜索河南地区网站">河南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖北" role="button" tabindex="0" aria-label="搜索湖北地区网站">湖北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖南" role="button" tabindex="0" aria-label="搜索湖南地区网站">湖南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="广东" role="button" tabindex="0" aria-label="搜索广东地区网站">广东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="海南" role="button" tabindex="0" aria-label="搜索海南地区网站">海南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="四川" role="button" tabindex="0" aria-label="搜索四川地区网站">四川</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="贵州" role="button" tabindex="0" aria-label="搜索贵州地区网站">贵州</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="云南" role="button" tabindex="0" aria-label="搜索云南地区网站">云南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="陕西" role="button" tabindex="0" aria-label="搜索陕西地区网站">陕西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="甘肃" role="button" tabindex="0" aria-label="搜索甘肃地区网站">甘肃</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="青海" role="button" tabindex="0" aria-label="搜索青海地区网站">青海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="台湾" role="button" tabindex="0" aria-label="搜索台湾地区网站">台湾</a></li>

                        <!-- 自治区 -->
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="内蒙古" role="button" tabindex="0" aria-label="搜索内蒙古地区网站">内蒙古</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="广西" role="button" tabindex="0" aria-label="搜索广西地区网站">广西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="西藏" role="button" tabindex="0" aria-label="搜索西藏地区网站">西藏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="宁夏" role="button" tabindex="0" aria-label="搜索宁夏地区网站">宁夏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="新疆" role="button" tabindex="0" aria-label="搜索新疆地区网站">新疆</a></li>

                        <!-- 特别行政区 -->
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="香港" role="button" tabindex="0" aria-label="搜索香港地区网站">香港</a></li>
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="澳门" role="button" tabindex="0" aria-label="搜索澳门地区网站">澳门</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 分类目录（动态） ========== -->
            <dl id="hcatebox" class="clearfix" aria-label="网站分类目录">
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_categories(), 'cate');
$_smarty_tpl->tpl_vars['cate']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cate']->value) {
$_smarty_tpl->tpl_vars['cate']->do_else = false;
?>
                <?php if ($_smarty_tpl->tpl_vars['cate']->value['cate_mod'] == 'webdir') {?>
                <dt><a href="<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_link'];?>
" aria-label="查看<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
分类"><?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
子分类">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_categories($_smarty_tpl->tpl_vars['cate']->value['cate_id']), 'scate');
$_smarty_tpl->tpl_vars['scate']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['scate']->value) {
$_smarty_tpl->tpl_vars['scate']->do_else = false;
?>
                        <li><a href="<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_link'];?>
" aria-label="查看<?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
网站"><?php echo $_smarty_tpl->tpl_vars['scate']->value['cate_name'];?>
</a></li>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </ul>
                </dd>
                <?php }?>
                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 最新收录 ========== -->
            <div id="newbox" aria-labelledby="latest-sites-title">
                <h3 id="latest-sites-title"><i class="fas fa-plus-circle icon-blue" aria-hidden="true"></i>最新收录</h3>
                <ul class="newlist" role="list" aria-label="最新收录网站列表">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,14), 'new');
$_smarty_tpl->tpl_vars['new']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->do_else = false;
?>
                    <li>
                        <span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
 - <?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
" aria-label="访问<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
网站">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
"
                                 width="18" height="18" alt="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
网站图标 - 最新收录网站" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
网站favicon图标" loading="lazy" /><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
 - <small><?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
</small>
                            <?php if ($_smarty_tpl->tpl_vars['new']->value['is_today']) {?><span class="new-icon" aria-label="今日新增">new</span><?php }?>
                        </a>
                    </li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
<div class="blank10"></div>
            <!-- ========== 热门标签 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-tags icon-orange"></i>热门标签</h3>
                <div class="hot-tags-container">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_hot_tags(30), 'tag');
$_smarty_tpl->tpl_vars['tag']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['tag']->value) {
$_smarty_tpl->tpl_vars['tag']->do_else = false;
?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_link'];?>
" class="hot-tag" title="标签：<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
，共<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_count'];?>
个网站使用">
                        <?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>

                        <span class="tag-count"><?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_count'];?>
</span>
                    </a>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </div>
            </div>
<div class="blank10"></div>
            <!-- ========== 数据归档 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-archive icon-purple"></i>数据归档</h3>
                <ul class="arcbox-list">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_archives(), 'arr', false, 'year');
$_smarty_tpl->tpl_vars['arr']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['year']->value => $_smarty_tpl->tpl_vars['arr']->value) {
$_smarty_tpl->tpl_vars['arr']->do_else = false;
?>
                    <li>
                        <b><?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年</b>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['arr']->value, 'item', false, 'month');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['month']->value => $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?>
                        <a href="javascript:void(0);"
                           class="archive-month-link <?php if ($_smarty_tpl->tpl_vars['year']->value == '2025' && $_smarty_tpl->tpl_vars['month']->value == '07') {?>active<?php }?>"
                           data-year="<?php echo $_smarty_tpl->tpl_vars['year']->value;?>
"
                           data-month="<?php echo $_smarty_tpl->tpl_vars['month']->value;?>
"
                           title="<?php echo $_smarty_tpl->tpl_vars['year']->value;?>
年<?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月共收录<?php echo $_smarty_tpl->tpl_vars['item']->value['site_count'];?>
个优秀站点">
                            <?php echo $_smarty_tpl->tpl_vars['month']->value;?>
月
                        </a>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>

                <!-- 网站列表显示区域 - 完全按照最新收录的结构 -->
                <div class="archive-content-container">
                    <div id="archive-loading" class="archive-loading hidden">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                    <ul id="archive-list" class="newlist">
                        <!-- 网站列表将通过AJAX加载，结构与最新收录完全一致 -->
                    </ul>
                    <div id="archive-error" class="archive-error hidden">
                        加载失败，请稍后重试
                    </div>
                </div>
            </div>
        </div><!-- /homebox-left -->

        <!-- ================= 右侧区域 ================= -->
        <div id="homebox-right" role="complementary" aria-label="推荐内容和导航">
            <!-- 站长推荐 - 带选项卡切换 -->
            <div id="bestbox" aria-labelledby="recommend-title">
                <!-- 选项卡标题 -->
                <div class="tab-header">
                    <h3 id="recommend-title" class="tab-item active" data-tab="recommend">
                        <span class="tab-recommend">
                            <i class="fas fa-crown icon-crown" aria-hidden="true"></i>站长推荐
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="pending">
                        <span class="tab-pending">
                            <i class="fas fa-clock icon-clock" aria-hidden="true"></i>待审核
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="blacklist">
                        <span class="tab-blacklist">
                            <i class="fas fa-ban icon-ban" aria-hidden="true"></i>黑名单
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="rejected">
                        <span class="tab-rejected">
                            <i class="fas fa-times-circle icon-times" aria-hidden="true"></i>审核不通过
                        </span>
                    </h3>
                </div>

                <!-- 选项卡内容 -->
                <div class="tab-content">
                    <!-- 站长推荐内容 -->
                    <div class="tab-pane active" id="recommend-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="站长推荐网站列表">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,35,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                            <li class="recommend-item">
                                <a href="/go.php?url=http://<?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
" class="recommend-link" aria-label="访问推荐网站<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
"
                                             width="28" height="28" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
网站图标" loading="lazy" />
                                        <div class="recommend-badge" aria-label="推荐标识">推荐</div>
                                    </div>
                                    <span class="recommend-name"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</span>
                                </a>
                            </li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                    </div>

                    <!-- 待审核内容 -->
                    <div class="tab-pane" id="pending-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="待审核网站列表">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_pending_websites(35), 'pending');
$_smarty_tpl->tpl_vars['pending']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['pending']->value) {
$_smarty_tpl->tpl_vars['pending']->do_else = false;
?>
                            <li class="recommend-item pending-item">
                                <a href="<?php echo $_smarty_tpl->tpl_vars['pending']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['pending']->value['web_name'];?>
 - 待审核网站" class="recommend-link" aria-label="查看待审核网站<?php echo $_smarty_tpl->tpl_vars['pending']->value['web_name'];?>
">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['pending']->value['web_url'];?>
"
                                             width="28" height="28" alt="<?php echo $_smarty_tpl->tpl_vars['pending']->value['web_name'];?>
网站图标" loading="lazy" class="img-filter-pending" />
                                        <div class="recommend-badge pending-badge" aria-label="待审核标识">待审</div>
                                    </div>
                                    <span class="recommend-name"><?php echo $_smarty_tpl->tpl_vars['pending']->value['web_name'];?>
</span>
                                </a>
                            </li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                    </div>

                    <!-- 黑名单内容 -->
                    <div class="tab-pane" id="blacklist-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="黑名单网站列表">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_blacklist_websites(35), 'blacklist');
$_smarty_tpl->tpl_vars['blacklist']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['blacklist']->value) {
$_smarty_tpl->tpl_vars['blacklist']->do_else = false;
?>
                            <li class="recommend-item blacklist-item">
                                <a href="?mod=blacklist_detail&id=<?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_id'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_name'];?>
 - 黑名单网站" class="recommend-link" aria-label="查看黑名单网站<?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_name'];?>
">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_url'];?>
"
                                             width="28" height="28" alt="<?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_name'];?>
网站图标" loading="lazy" class="img-filter-blacklist" />
                                        <div class="recommend-badge blacklist-badge" aria-label="黑名单标识">黑名单</div>
                                    </div>
                                    <span class="recommend-name"><?php echo $_smarty_tpl->tpl_vars['blacklist']->value['web_name'];?>
</span>
                                </a>
                            </li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                    </div>

                    <!-- 审核不通过内容 -->
                    <div class="tab-pane" id="rejected-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="审核不通过网站列表">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_rejected_websites(35), 'rejected');
$_smarty_tpl->tpl_vars['rejected']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['rejected']->value) {
$_smarty_tpl->tpl_vars['rejected']->do_else = false;
?>
                            <li class="recommend-item rejected-item">
                                <a href="?mod=rejected_detail&id=<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_id'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_name'];?>
 - 审核不通过网站<?php if ($_smarty_tpl->tpl_vars['rejected']->value['web_reject_reason']) {?> - 原因：<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_reject_reason'];
}?>" class="recommend-link" aria-label="查看审核不通过网站<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_name'];?>
">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_url'];?>
"
                                             width="28" height="28" alt="<?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_name'];?>
网站图标" loading="lazy" class="img-filter-rejected" />
                                        <div class="recommend-badge rejected-badge" aria-label="审核不通过标识">不通过</div>
                                    </div>
                                    <span class="recommend-name"><?php echo $_smarty_tpl->tpl_vars['rejected']->value['web_name'];?>
</span>
                                </a>
                            </li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 热门排行榜 -->
            <div id="toprank-box" class="clearfix">
                <h3>
                    <span style="color: #ff6b6b; font-weight: bold; text-shadow: 0 1px 2px rgba(255, 107, 107, 0.3); font-size: 14px;">
                        <i class="fas fa-trophy" style="color: #ffd700; margin-right: 5px;"></i>热门排行榜TOP30
                    </span>
                    <a href="?mod=top" style="color: #666; font-size: 12px; text-decoration: none; float: right;" title="查看完整排行榜">更多 &raquo;</a>
                </h3>
                <ul class="clearfix bestlist-enhanced toprank-list">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,30,false,false,'views','desc'), 'rank', false, NULL, 'top_rank', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['rank']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['rank']->value) {
$_smarty_tpl->tpl_vars['rank']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration']++;
?>
                    <li class="recommend-item toprank-item">
                        <a href="<?php echo $_smarty_tpl->tpl_vars['rank']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['rank']->value['web_name'];?>
" class="recommend-link toprank-link">
                            <div class="recommend-icon">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['rank']->value['web_url'];?>
"
                                     width="28" height="28" alt="<?php echo $_smarty_tpl->tpl_vars['rank']->value['web_name'];?>
网站图标" loading="lazy" />
                                <div class="recommend-badge toprank-badge rank-<?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration'] : null);?>
">
                                    <?php if ((isset($_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration'] : null) <= 3) {?>
                                        <i class="fas fa-trophy"></i><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration'] : null);?>

                                    <?php } else { ?>
                                        <?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_top_rank']->value['iteration'] : null);?>

                                    <?php }?>
                                </div>
                            </div>
                            <span class="recommend-name"><?php echo $_smarty_tpl->tpl_vars['rank']->value['web_name'];?>
</span>
                            <div class="toprank-stats">
                                <span class="rank-views">
                                    <i class="fas fa-eye"></i><?php echo $_smarty_tpl->tpl_vars['rank']->value['web_views'];?>

                                </span>
                            </div>
                        </a>
                    </li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>


            <!-- 在线音乐播放器 -->
            <div id="musicbox" class="clearfix" aria-labelledby="music-player-title">
                <h3 id="music-player-title">
                    <span class="music-title-span">
                        <i class="fas fa-music icon-pink" aria-hidden="true"></i>在线音乐
                    </span>
                </h3>
                <div id="music-player-container">
                    <!-- 音乐播放器控制面板 -->
                    <div class="music-controls">
                        <button id="prevBtn" class="control-btn" title="上一首">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button id="playBtn" class="control-btn play-pause" title="播放/暂停">
                            <i class="fas fa-play"></i>
                        </button>
                        <button id="nextBtn" class="control-btn" title="下一首">
                            <i class="fas fa-step-forward"></i>
                        </button>
                        <button id="volumeBtn" class="control-btn" title="静音/取消静音">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <div class="volume-control">
                            <input type="range" id="volumeSlider" min="0" max="100" value="50" class="volume-slider" title="音量控制">
                        </div>
                    </div>

                    <!-- 当前播放信息 -->
                    <div class="music-info">
                        <div class="music-title" id="currentTitle">选择一首歌曲开始播放</div>
                        <div class="music-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="time-info">
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                            </div>
                        </div>
                    </div>

                    <!-- 音乐列表 -->
                    <div class="music-list">
                        <div class="list-header">
                            <span>播放列表</span>
                            <button id="refreshList" class="refresh-btn" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <ul id="musicList" class="music-items">
                            <!-- 音乐列表将通过JavaScript动态加载 -->
                            <li class="loading-item">正在加载音乐列表...</li>
                        </ul>
                    </div>

                    <!-- 隐藏的音频元素 -->
                    <audio id="audioPlayer" preload="none"></audio>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 酷站导航 -->
            <div id="coolbox" class="clearfix">
                <h3><i class="fas fa-star icon-gold"></i>酷站导航</h3>
                <ul class="csitelist">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_best_categories(), 'cate', false, NULL, 'csite', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['cate']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cate']->value) {
$_smarty_tpl->tpl_vars['cate']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_csite']->value['iteration']++;
?>
                    <li>
                        <h4><a href="<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
</a></h4>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_link'];?>
" class="more">更多>></a>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites($_smarty_tpl->tpl_vars['cate']->value['cate_id'],8), 'cool');
$_smarty_tpl->tpl_vars['cool']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cool']->value) {
$_smarty_tpl->tpl_vars['cool']->do_else = false;
?>
                        <span>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['cool']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['cool']->value['web_name'];?>
">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['cool']->value['web_url'];?>
"
                                     width="18" height="18" /><?php echo $_smarty_tpl->tpl_vars['cool']->value['web_name'];?>

                            </a>
                        </span>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </li>
                    <?php if ((isset($_smarty_tpl->tpl_vars['__smarty_foreach_csite']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_csite']->value['iteration'] : null)%5 == 0 && (isset($_smarty_tpl->tpl_vars['__smarty_foreach_csite']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_csite']->value['iteration'] : null) != 20) {?>
                    <li class="sline"></li>
                    <?php }?>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 站点资讯 + 链接交换 -->
            <div id="rowbox" class="clearfix">
                <div id="newsbox">
                    <h3><i class="fas fa-newspaper icon-info"></i>站点资讯</h3>
                    <span1><a href="?mod=article" class="more">更多>></a></span1>

                    <!-- 文章分类导航 -->
                    <div class="article-categories">
                        <a href="javascript:void(0);" class="article-cate-link active" data-cate-id="0">最新</a>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_categories(), 'cate');
$_smarty_tpl->tpl_vars['cate']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cate']->value) {
$_smarty_tpl->tpl_vars['cate']->do_else = false;
?>
                        <?php if ($_smarty_tpl->tpl_vars['cate']->value['cate_mod'] == 'article' && $_smarty_tpl->tpl_vars['cate']->value['cate_postcount'] > 0) {?>
                        <a href="javascript:void(0);" class="article-cate-link" data-cate-id="<?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['cate']->value['cate_name'];?>
</a>
                        <?php }?>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </div>

                    <!-- 文章列表 -->
                    <div class="article-content-container">
                        <ul id="article-list" class="newslist">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,10,false), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                            <li data-number="<?php echo $_smarty_tpl->tpl_vars['idx']->value+1;?>
">
                                <span><?php echo $_smarty_tpl->tpl_vars['art']->value['art_ctime'];?>
</span>
                                <a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
">
                                    <?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>

                                    <?php if ($_smarty_tpl->tpl_vars['art']->value['is_today']) {?><span class="new-icon">new</span><?php }?>
                                </a>
                            </li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                        <div id="article-error" class="archive-error hidden">
                            暂无文章
                        </div>
                    </div>
                </div>

                <div class="line"></div>

                <div id="exlink">
                    <h3><i class="fas fa-handshake icon-warning"></i>链接交换</h3>
                    <ul class="exlist">
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_weblinks(0,8), 'link');
$_smarty_tpl->tpl_vars['link']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['link']->value) {
$_smarty_tpl->tpl_vars['link']->do_else = false;
?>
                        <li>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['link']->value['web_link'];?>
">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['link']->value['web_url'];?>
" width="18" height="18" />
                                <?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>
 - <img src="module/dr_badge.php?domain=<?php echo $_smarty_tpl->tpl_vars['link']->value['web_url'];?>
&size=small" alt="DR Badge" class="dr-badge" />，
                                百度收录<?php echo $_smarty_tpl->tpl_vars['link']->value['web_grank'];?>
，<?php echo $_smarty_tpl->tpl_vars['link']->value['deal_type'];?>
友情链接
                            </a>
                        </li>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </ul>
                </div>
            </div>
        </div><!-- /homebox-right -->
    </div><!-- /homebox -->

    <div class="blank10"></div>

    <!-- ================= 最新点入 / 出 / 友情链接 ================= -->
    <div id="inbox" class="clearfix" aria-labelledby="latest-visits-title">
        <h3 id="latest-visits-title"><i class="fas fa-sign-in-alt icon-success" aria-hidden="true"></i>最新点入</h3>
        <ul class="inlist" role="list" aria-label="最新点入网站列表">
            
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,36,false,false,'instat'), 'instat');
$_smarty_tpl->tpl_vars['instat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->do_else = false;
?>
            <li>
                <a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
" aria-label="访问<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_url'];?>
"
                         width="16" height="16" alt="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
图标" loading="lazy" /><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>

                </a>
            </li>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
            
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="latest-outbound-title">
        <h3 id="latest-outbound-title"><i class="fas fa-sign-out-alt icon-danger" aria-hidden="true"></i>最新点出</h3>
        <ul class="inlist" role="list" aria-label="最新点出网站列表">
            
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,36,false,false,'outstat'), 'outstat');
$_smarty_tpl->tpl_vars['outstat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->do_else = false;
?>
            <li>
                <a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
" aria-label="访问<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_url'];?>
"
                         width="16" height="16" alt="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
图标" loading="lazy" /><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>

                </a>
            </li>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
            
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="friend-links-title">
        <h3 id="friend-links-title"><i class="fas fa-heart icon-pink" aria-hidden="true"></i>友情链接</h3>
        <ul class="inlist" role="list" aria-label="友情链接列表">
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_links(), 'link');
$_smarty_tpl->tpl_vars['link']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['link']->value) {
$_smarty_tpl->tpl_vars['link']->do_else = false;
?>
            <li>
                <a href="<?php echo $_smarty_tpl->tpl_vars['link']->value['link_url'];?>
" target="_blank" rel="noopener noreferrer" title="<?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>
" aria-label="访问友情链接<?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>
">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=<?php echo $_smarty_tpl->tpl_vars['link']->value['link_url'];?>
"
                         width="16" height="16" alt="<?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>
图标" loading="lazy" /><?php echo $_smarty_tpl->tpl_vars['link']->value['link_name'];?>

                </a>
            </li>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </ul>
    </div>


    <div class="blank10"></div>

    <!-- 今日访问统计 -->
    <div id="todayStatsBox" class="clearfix">
        <h3>📊 今日访问统计</h3>
        <div class="stats-grid">
            <div class="stat-card" data-tooltip="今日网站总访问量">
                <div class="stat-icon">👁️</div>
                <div class="stat-label">总浏览</div>
                <div class="stat-value" id="todayTotalVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日站点页面访问量">
                <div class="stat-icon">🏠</div>
                <div class="stat-label">站点浏览</div>
                <div class="stat-value" id="todaySiteVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日文章页面访问量">
                <div class="stat-icon">📄</div>
                <div class="stat-label">文章浏览</div>
                <div class="stat-value" id="todayArticleVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日用户点击外链次数">
                <div class="stat-icon">🔗</div>
                <div class="stat-label">出站次数</div>
                <div class="stat-value" id="todayOutlinks">加载中...</div>
            </div>
        </div>
    </div>

    <!-- 当日蜘蛛统计 -->
    <div class="blank10"></div>
    <div id="spiderStatsBox" class="clearfix">
        <h3>🕷️ 当日蜘蛛统计</h3>
        <div class="spider-grid">
            <div class="spider-card" data-tooltip="Google搜索引擎爬虫访问次数">
                <div class="spider-icon spider-google">G</div>
                <div class="spider-label">Google</div>
                <div class="spider-value" id="spiderGoogle">0</div>
            </div>
            <div class="spider-card" data-tooltip="百度搜索引擎爬虫访问次数">
                <div class="spider-icon spider-baidu">百</div>
                <div class="spider-label">Baidu</div>
                <div class="spider-value" id="spiderBaidu">0</div>
            </div>
            <div class="spider-card" data-tooltip="Bing搜索引擎爬虫访问次数">
                <div class="spider-icon spider-bing">B</div>
                <div class="spider-label">Bing</div>
                <div class="spider-value" id="spiderBing">0</div>
            </div>
            <div class="spider-card" data-tooltip="搜狗搜索引擎爬虫访问次数">
                <div class="spider-icon spider-sogou">搜</div>
                <div class="spider-label">Sogou</div>
                <div class="spider-value" id="spiderSogou">0</div>
            </div>
            <div class="spider-card" data-tooltip="360搜索引擎爬虫访问次数">
                <div class="spider-icon spider-360">360</div>
                <div class="spider-label">360</div>
                <div class="spider-value" id="spiderSo360">0</div>
            </div>
            <div class="spider-card" data-tooltip="字节跳动搜索爬虫访问次数">
                <div class="spider-icon spider-bytedance">字</div>
                <div class="spider-label">Byte</div>
                <div class="spider-value" id="spiderBytedance">0</div>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div><!-- /wrapper -->

<!-- SEO优化 - 结构化数据：网站导航 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SiteNavigationElement",
    "name": "网站导航",
    "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
",
    "hasPart": [
        {
            "@type": "SiteNavigationElement",
            "name": "网站目录",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "VIP站点",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=vip_list"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "站长资讯",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=article"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "链接交换",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=weblink"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "最新收录",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=update"
        }
    ]
}
<?php echo '</script'; ?>
>

<!-- SEO优化 - 结构化数据：网站统计信息 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Dataset",
    "name": "95目录网站统计",
    "description": "95目录网收录的网站统计数据",
    "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
",
    "keywords": ["网站目录", "网站收录", "网站分类", "站长工具"],
    "creator": {
        "@type": "Organization",
        "name": "95目录网"
    }
}
<?php echo '</script'; ?>
>

<!-- JavaScript功能已移至common.js文件中 -->



</body>
</html><?php }
}
