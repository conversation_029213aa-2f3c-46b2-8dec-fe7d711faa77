
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;




CREATE TABLE IF NOT EXISTS `dir_advers` (
  `adver_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `adver_type` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `adver_name` varchar(50) NOT NULL DEFAULT '',
  `adver_url` varchar(255) NOT NULL,
  `adver_code` text NOT NULL,
  `adver_etips` varchar(50) NOT NULL DEFAULT '',
  `adver_days` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `adver_date` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`adver_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_articles` (
  `art_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `cate_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `art_title` varchar(100) NOT NULL,
  `art_tags` varchar(50) NOT NULL,
  `copy_from` varchar(50) NOT NULL,
  `copy_url` varchar(200) NOT NULL,
  `art_intro` varchar(200) NOT NULL,
  `art_content` text NOT NULL,
  `art_views` int(10) unsigned NOT NULL DEFAULT '0',
  `art_ispay` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `art_istop` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `art_isbest` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `art_status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `art_ctime` int(10) unsigned NOT NULL DEFAULT '0',
  `art_utime` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`art_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_categories` (
  `cate_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `root_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `cate_mod` enum('webdir','article') NOT NULL DEFAULT 'webdir',
  `cate_name` varchar(50) NOT NULL DEFAULT '',
  `cate_dir` varchar(50) NOT NULL DEFAULT '',
  `cate_url` varchar(255) NOT NULL,
  `cate_isbest` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `cate_order` smallint(5) unsigned NOT NULL DEFAULT '0',
  `cate_keywords` varchar(100) NOT NULL,
  `cate_description` varchar(255) NOT NULL DEFAULT '',
  `cate_arrparentid` varchar(255) NOT NULL,
  `cate_arrchildid` text NOT NULL,
  `cate_childcount` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `cate_postcount` smallint(5) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`cate_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_favorites` (
  `fav_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `fav_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`fav_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_feedbacks` (
  `fb_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `fb_nick` varchar(50) NOT NULL,
  `fb_email` varchar(50) NOT NULL DEFAULT '',
  `fb_content` text NOT NULL,
  `fb_date` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`fb_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_labels` (
  `label_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `label_name` varchar(50) NOT NULL DEFAULT '',
  `label_intro` varchar(255) NOT NULL DEFAULT '',
  `label_content` text NOT NULL,
  PRIMARY KEY (`label_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_links` (
  `link_id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
  `link_name` varchar(50) NOT NULL DEFAULT '',
  `link_url` varchar(255) NOT NULL DEFAULT '',
  `link_logo` varchar(255) NOT NULL DEFAULT '',
  `link_hide` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_order` tinyint(3) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`link_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_options` (
  `option_name` varchar(30) NOT NULL DEFAULT '',
  `option_value` text NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_pages` (
  `page_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `page_name` varchar(50) NOT NULL DEFAULT '',
  `page_intro` varchar(255) NOT NULL DEFAULT '',
  `page_content` text NOT NULL,
  PRIMARY KEY (`page_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_users` (
  `user_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','member','recruit','vip') NOT NULL DEFAULT 'member',
  `user_email` varchar(50) NOT NULL,
  `user_pass` char(32) NOT NULL,
  `open_id` char(32) NOT NULL,
  `nick_name` varchar(20) NOT NULL,
  `user_qq` varchar(20) NOT NULL,
  `user_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `verify_code` varchar(32) NOT NULL,
  `user_status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `join_time` int(10) unsigned NOT NULL DEFAULT '0',
  `login_time` int(10) unsigned NOT NULL DEFAULT '0',
  `login_ip` int(10) unsigned NOT NULL DEFAULT '0',
  `login_count` smallint(5) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_webdata` (
  `web_id` int(10) unsigned NOT NULL,
  `web_ip` int(10) unsigned NOT NULL DEFAULT '0',
  `web_grank` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `web_brank` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `web_srank` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `web_arank` int(10) unsigned NOT NULL DEFAULT '0',
  `web_instat` int(10) unsigned NOT NULL DEFAULT '0',
  `web_outstat` int(10) unsigned NOT NULL DEFAULT '0',
  `web_fnum` int(10) unsigned NOT NULL DEFAULT '0',
  `web_views` int(10) unsigned NOT NULL DEFAULT '0',
  `web_errors` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `web_itime` int(10) unsigned NOT NULL DEFAULT '0',
  `web_otime` int(10) unsigned NOT NULL DEFAULT '0',
  `web_utime` int(10) unsigned NOT NULL DEFAULT '0',
  `rate_score` int(10) unsigned NOT NULL DEFAULT '0',
  `rate_count` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`web_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_weblinks` (
  `link_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `deal_type` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_name` varchar(20) NOT NULL,
  `link_type` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_pos` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_price` smallint(3) unsigned NOT NULL DEFAULT '0',
  `link_if1` int(10) unsigned NOT NULL,
  `link_if2` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_if3` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `link_if4` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `link_intro` varchar(200) NOT NULL,
  `link_days` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `link_views` int(10) unsigned NOT NULL DEFAULT '0',
  `link_istop` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_hide` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `link_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`link_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_websites` (
  `web_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `cate_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `web_name` varchar(100) NOT NULL DEFAULT '',
  `web_url` varchar(255) NOT NULL DEFAULT '',
  `web_tags` varchar(100) NOT NULL,
  `web_pic` varchar(100) NOT NULL,
  `web_intro` text NOT NULL,
  `web_ispay` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_istop` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_isbest` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_islink` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_ctime` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`web_id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_spider_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL,
  `google_count` int(10) unsigned NOT NULL DEFAULT '0',
  `baidu_count` int(10) unsigned NOT NULL DEFAULT '0',
  `bing_count` int(10) unsigned NOT NULL DEFAULT '0',
  `yandex_count` int(10) unsigned NOT NULL DEFAULT '0',
  `sogou_count` int(10) unsigned NOT NULL DEFAULT '0',
  `so360_count` int(10) unsigned NOT NULL DEFAULT '0',
  `bytedance_count` int(10) unsigned NOT NULL DEFAULT '0',
  `yahoo_count` int(10) unsigned NOT NULL DEFAULT '0',
  `other_count` int(10) unsigned NOT NULL DEFAULT '0',
  `total_visits` int(10) unsigned NOT NULL DEFAULT '0',
  `total_sites` int(10) unsigned NOT NULL DEFAULT '0',
  `total_articles` int(10) unsigned NOT NULL DEFAULT '0',
  `total_outlinks` int(10) unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_date` (`stat_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_website_comments` (
  `comment_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `web_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '网站ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，0表示匿名',
  `user_email` varchar(50) NOT NULL DEFAULT '' COMMENT '用户邮箱',
  `user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `user_ip` varchar(45) NOT NULL DEFAULT '' COMMENT '用户IP地址',
  `content_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '内容质量评分1-5星',
  `service_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站服务评分1-5星',
  `trust_level` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站诚信评分1-5星',
  `comment_content` text NOT NULL COMMENT '评论内容',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父评论ID，0表示主评论',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=隐藏',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`comment_id`),
  KEY `web_id` (`web_id`),
  KEY `user_id` (`user_id`),
  KEY `parent_id` (`parent_id`),
  KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `dir_payment_records` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_name` varchar(100) NOT NULL DEFAULT '',
  `web_url` varchar(255) NOT NULL DEFAULT '',
  `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付费金额',
  `payment_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付费时间',
  `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '到期时间',
  `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已过期',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `web_id` (`web_id`),
  KEY `payment_type` (`payment_type`),
  KEY `payment_time` (`payment_time`),
  KEY `expire_time` (`expire_time`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;



INSERT INTO `dir_options` (`option_name`, `option_value`) VALUES
('site_name', '35分类目录'),
('site_title', '网址大全_网址目录_上网导航_网站提交/登录入口'),
('site_url', 'http://www.35dir.com/'),
('site_root', '/'),
('admin_email', '<EMAIL>'),
('site_keywords', '分类目录,网站收录,网站提交,网站目录,网站推广,网站登录'),
('site_description', '全人工编辑的开放式网站分类目录，收录国内外、各行业优秀网站，旨在为用户提供网站分类目录检索、优秀网站参考、网站推广服务。'),
('site_copyright', 'Copyright &copy; 2008-2011 35dir.com All Rights Reserved'),
('register_email_verify', 'no'),
('is_enabled_register', 'yes'),
('site_notice', '这是一条测试公告！'),
('filter_words', 'sb'),
('search_words', '分类目录,网站目录,网址大全'),
('upload_dir', 'uploads'),
('article_link_num', '3'),
('data_update_cycle', '3'),
('check_link_url', 'www.35dir.com'),
('check_link_name', '35分类目录'),
('is_enabled_linkcheck', 'yes'),
('submit_close_reason', ''),
('is_enabled_submit', 'yes'),
('is_enabled_gzip', 'yes'),
('link_struct', '0'),
('qq_appkey', 'app_key'),
('qq_appid', 'app_id'),
('is_enabled_connect', 'yes'),
('smtp_host', 'smtp.qq.com'),
('smtp_port', '25'),
('smtp_auth', 'yes'),
('smtp_user', 'test'),
('smtp_pass', 'test');