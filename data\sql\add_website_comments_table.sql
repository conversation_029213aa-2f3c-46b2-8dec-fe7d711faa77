-- 为网站目录系统添加评论功能数据库表
-- 执行此脚本前请备份数据库
-- 执行方法：在数据库管理工具中运行此SQL脚本

-- 创建网站评论表
CREATE TABLE IF NOT EXISTS `dir_website_comments` (
  `comment_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `web_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '网站ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，0表示匿名',
  `user_email` varchar(50) NOT NULL DEFAULT '' COMMENT '用户邮箱',
  `user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `user_ip` varchar(45) NOT NULL DEFAULT '' COMMENT '用户IP地址',
  `content_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '内容质量评分1-5星',
  `service_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站服务评分1-5星',
  `trust_level` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站诚信评分1-5星',
  `comment_content` text NOT NULL COMMENT '评论内容',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父评论ID，0表示主评论',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=隐藏',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`comment_id`),
  KEY `web_id` (`web_id`),
  KEY `user_id` (`user_id`),
  KEY `parent_id` (`parent_id`),
  KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 验证表创建结果
DESCRIBE `dir_website_comments`;
