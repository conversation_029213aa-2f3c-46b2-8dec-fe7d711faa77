<?php
/*
 * <AUTHOR> 95DIR安全测试
 * @Description  : 简化版安全测试工具
 * @Version      : 1.0
 * @Date         : 2025-08-07
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 测试结果数组
$test_results = array();
$error_messages = array();

// 测试1：基础文件检查
try {
    if (file_exists(APP_PATH . 'include/function.php')) {
        $test_results['function_file'] = '✅ function.php 文件存在';
    } else {
        $test_results['function_file'] = '❌ function.php 文件不存在';
    }
} catch (Exception $e) {
    $error_messages[] = '文件检查错误: ' . $e->getMessage();
}

// 测试2：加载基础文件
try {
    require_once(APP_PATH . 'include/function.php');
    $test_results['function_load'] = '✅ function.php 加载成功';
} catch (Exception $e) {
    $test_results['function_load'] = '❌ function.php 加载失败: ' . $e->getMessage();
    $error_messages[] = 'function.php 加载错误: ' . $e->getMessage();
}

// 测试3：版本文件
try {
    if (file_exists(APP_PATH . 'version.php')) {
        require_once(APP_PATH . 'version.php');
        $version = defined('SYS_VERSION') ? SYS_VERSION : '未知';
        $test_results['version'] = '✅ 版本文件加载成功，版本: ' . $version;
    } else {
        $test_results['version'] = '❌ 版本文件不存在';
    }
} catch (Exception $e) {
    $test_results['version'] = '❌ 版本文件加载失败: ' . $e->getMessage();
    $error_messages[] = '版本文件错误: ' . $e->getMessage();
}

// 测试4：安全核心文件
try {
    if (file_exists(APP_PATH . 'include/security_core.php')) {
        require_once(APP_PATH . 'include/security_core.php');
        $test_results['security_core'] = '✅ 安全核心文件加载成功';
        
        // 测试SecurityCore类
        if (class_exists('SecurityCore')) {
            $security = SecurityCore::getInstance();
            $test_results['security_class'] = '✅ SecurityCore 类实例化成功';
            
            // 测试动态密钥生成
            $key = $security->generate_dynamic_key('test');
            if (strlen($key) > 0) {
                $test_results['dynamic_key'] = '✅ 动态密钥生成成功，长度: ' . strlen($key);
            } else {
                $test_results['dynamic_key'] = '❌ 动态密钥生成失败';
            }
        } else {
            $test_results['security_class'] = '❌ SecurityCore 类不存在';
        }
    } else {
        $test_results['security_core'] = '❌ 安全核心文件不存在';
    }
} catch (Exception $e) {
    $test_results['security_core'] = '❌ 安全核心文件加载失败: ' . $e->getMessage();
    $error_messages[] = '安全核心错误: ' . $e->getMessage();
}

// 测试5：完整性保护文件
try {
    if (file_exists(APP_PATH . 'include/integrity_guard.php')) {
        require_once(APP_PATH . 'include/integrity_guard.php');
        $test_results['integrity_guard'] = '✅ 完整性保护文件加载成功';
        
        if (class_exists('IntegrityGuard')) {
            $guard = IntegrityGuard::getInstance();
            $test_results['integrity_class'] = '✅ IntegrityGuard 类实例化成功';
        } else {
            $test_results['integrity_class'] = '❌ IntegrityGuard 类不存在';
        }
    } else {
        $test_results['integrity_guard'] = '❌ 完整性保护文件不存在';
    }
} catch (Exception $e) {
    $test_results['integrity_guard'] = '❌ 完整性保护文件加载失败: ' . $e->getMessage();
    $error_messages[] = '完整性保护错误: ' . $e->getMessage();
}

// 测试6：授权文件
try {
    if (file_exists(APP_PATH . 'include/license.php')) {
        require_once(APP_PATH . 'include/license.php');
        $test_results['license_file'] = '✅ 授权文件加载成功';
        
        if (class_exists('LicenseValidator')) {
            $validator = new LicenseValidator();
            $test_results['license_class'] = '✅ LicenseValidator 类实例化成功';
            
            // 测试机器码生成
            $machine_code = $validator->generate_machine_code();
            if (strlen($machine_code) > 0) {
                $test_results['machine_code'] = '✅ 机器码生成成功: ' . $machine_code;
            } else {
                $test_results['machine_code'] = '❌ 机器码生成失败';
            }
        } else {
            $test_results['license_class'] = '❌ LicenseValidator 类不存在';
        }
    } else {
        $test_results['license_file'] = '❌ 授权文件不存在';
    }
} catch (Exception $e) {
    $test_results['license_file'] = '❌ 授权文件加载失败: ' . $e->getMessage();
    $error_messages[] = '授权文件错误: ' . $e->getMessage();
}

// 测试7：授权数据文件
try {
    $license_data_file = ROOT_PATH . 'data/license.dat';
    if (file_exists($license_data_file)) {
        $test_results['license_data'] = '✅ 授权数据文件存在';
        
        // 检查文件大小
        $file_size = filesize($license_data_file);
        $test_results['license_size'] = '✅ 授权文件大小: ' . $file_size . ' 字节';
        
        // 检查文件修改时间
        $file_time = filemtime($license_data_file);
        $test_results['license_time'] = '✅ 授权文件时间: ' . date('Y-m-d H:i:s', $file_time);
    } else {
        $test_results['license_data'] = '❌ 授权数据文件不存在';
    }
} catch (Exception $e) {
    $test_results['license_data'] = '❌ 授权数据文件检查失败: ' . $e->getMessage();
    $error_messages[] = '授权数据文件错误: ' . $e->getMessage();
}

// 测试8：函数存在性检查
$functions_to_check = array(
    'authcode' => 'authcode 加密函数',
    'verify_system_license' => '系统授权验证函数',
    'advanced_security_check' => '高级安全检查函数',
    'random_security_verify' => '随机安全验证函数',
    'integrity_guard_check' => '完整性保护检查函数'
);

foreach ($functions_to_check as $func => $desc) {
    if (function_exists($func)) {
        $test_results['func_' . $func] = '✅ ' . $desc . ' 存在';
    } else {
        $test_results['func_' . $func] = '❌ ' . $desc . ' 不存在';
    }
}

// 计算成功率
$total_tests = count($test_results);
$passed_tests = 0;
foreach ($test_results as $result) {
    if (strpos($result, '✅') === 0) {
        $passed_tests++;
    }
}
$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0;

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR简化安全测试</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-item { margin-bottom: 10px; padding: 10px; border-radius: 4px; font-family: monospace; }
        .test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .summary { background: #e9ecef; padding: 20px; border-radius: 6px; margin: 20px 0; text-align: center; }
        .error-box { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR简化安全测试</div>
            <p>检查系统基础功能和安全模块</p>
        </div>
        
        <div class="summary">
            <h3>测试总结</h3>
            <p><strong>总测试项：</strong><?php echo $total_tests; ?></p>
            <p><strong>通过测试：</strong><?php echo $passed_tests; ?></p>
            <p><strong>成功率：</strong><?php echo $success_rate; ?>%</p>
        </div>
        
        <?php if (!empty($error_messages)): ?>
        <div class="error-box">
            <h4>错误信息：</h4>
            <?php foreach ($error_messages as $error): ?>
                <p><?php echo htmlspecialchars($error); ?></p>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <h3>详细测试结果：</h3>
        <?php foreach ($test_results as $test => $result): ?>
            <div class="test-item <?php echo strpos($result, '✅') === 0 ? 'test-success' : 'test-error'; ?>">
                <?php echo htmlspecialchars($result); ?>
            </div>
        <?php endforeach; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="advanced_license_generator.php" class="btn">生成授权文件</a>
            <a href="license_test.php" class="btn">基础授权测试</a>
            <a href="../index.php" class="btn">返回首页</a>
            <a href="javascript:location.reload()" class="btn">重新测试</a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>说明：</h4>
            <ul>
                <li>此工具用于检查系统基础功能是否正常</li>
                <li>如果有错误，请根据错误信息进行修复</li>
                <li>所有测试通过后，可以使用完整的安全测试工具</li>
            </ul>
        </div>
    </div>
</body>
</html>
