{#include file="header.html"#}

<h3 class="title"><em>{#$pagetitle#}</em></h3>
<div style="padding: 15px;">
    <form method="post" action="{#$fileurl#}">
        <table width="100%" border="0" cellspacing="1" cellpadding="0">
            <tr>
                <td width="120" align="right">百度推送Token：</td>
                <td>
                    <input type="text" name="baidu_token" value="{#$config.baidu_token#}" size="50" class="ipt" />
                    <div style="color: #666; font-size: 12px; margin-top: 5px;">
                        获取方式：登录百度站长平台 → 数据引入 → 链接提交 → 主动推送 → 获取Token
                    </div>
                </td>
            </tr>
            <tr>
                <td align="right">谷歌API密钥：</td>
                <td>
                    <input type="text" name="google_key" value="{#$config.google_key#}" size="50" class="ipt" />
                    <div style="color: #666; font-size: 12px; margin-top: 5px;">
                        获取方式：Google Cloud Console → APIs & Services → Credentials → Create API Key
                    </div>
                </td>
            </tr>
            <tr>
                <td align="right">必应API密钥：</td>
                <td>
                    <input type="text" name="bing_key" value="{#$config.bing_key#}" size="50" class="ipt" />
                    <div style="color: #666; font-size: 12px; margin-top: 5px;">
                        获取方式：Bing Webmaster Tools → Settings → API Access → Generate API Key
                    </div>
                </td>
            </tr>
            <tr>
                <td></td>
                <td>
                    <input type="submit" name="submit" value="保存配置" class="btn" />
                </td>
            </tr>
        </table>
    </form>
    
    <div style="margin-top: 20px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6;">
        <h4>功能说明：</h4>
        <p>这是搜索引擎推送功能的基础版本，目前支持API密钥的配置和保存。</p>
        <p>配置保存后，您可以使用这些API密钥来推送网站链接到各大搜索引擎。</p>
        
        <h4>当前配置状态：</h4>
        <ul>
            <li>百度推送Token：{#if $config.baidu_token#}<span style="color: green;">已配置</span>{#else#}<span style="color: red;">未配置</span>{#/if#}</li>
            <li>谷歌API密钥：{#if $config.google_key#}<span style="color: green;">已配置</span>{#else#}<span style="color: red;">未配置</span>{#/if#}</li>
            <li>必应API密钥：{#if $config.bing_key#}<span style="color: green;">已配置</span>{#else#}<span style="color: red;">未配置</span>{#/if#}</li>
        </ul>
    </div>
</div>

{#include file="footer.html"#}
