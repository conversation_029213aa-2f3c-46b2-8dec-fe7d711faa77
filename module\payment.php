<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

// 引入支付处理函数
if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
}

$pagename = '支付页面';
$tempfile = 'payment.html';
$pageurl = '?mod=payment';

// 获取订单号
$order_no = trim($_GET['order_no']);
if (empty($order_no)) {
    echo "<script>alert('订单号不能为空！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 获取订单信息
$order_table = $DB->table('payment_orders');
$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if (!$order) {
    echo "<script>alert('订单不存在！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 检查订单是否已过期
if ($order['expire_time'] < time()) {
    echo "<script>alert('订单已过期，请重新提交！'); window.location.href = '?mod=quicksubmit';</script>";
    exit;
}

// 检查订单是否已支付
if ($order['status'] == 1) {
    echo "<script>alert('订单已支付成功！'); window.location.href = '?mod=index';</script>";
    exit;
}

// 获取价格信息
$price_info = function_exists('get_payment_price') ? get_payment_price($order['payment_type']) : array('name' => '未知服务', 'price' => $order['amount']);

// 设置模板变量
$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
$smarty->assign('site_keywords', '网站收录支付,付费审核');
$smarty->assign('site_description', '网站收录付费支付页面');
$smarty->assign('site_path', '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &raquo; <a href="?mod=quicksubmit">快速提交</a> &raquo; '.$pagename);
$smarty->assign('pageurl', $pageurl);
$smarty->assign('order', $order);
$smarty->assign('price_info', $price_info);

// 计算剩余时间（秒）
$remaining_time = $order['expire_time'] - time();
$smarty->assign('remaining_time', $remaining_time);

smarty_output($tempfile);

?>
