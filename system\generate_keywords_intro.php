<?php
// generate_keywords_intro.php - 专门用于生成AI关键词和简介
header('Content-Type: application/json');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

// 获取提交的数据
$url = isset($_POST['url']) ? trim($_POST['url']) : '';
$meta_title = isset($_POST['meta_title']) ? trim($_POST['meta_title']) : '';
$meta_keywords = isset($_POST['meta_keywords']) ? trim($_POST['meta_keywords']) : '';
$meta_description = isset($_POST['meta_description']) ? trim($_POST['meta_description']) : '';

// 验证必需参数
if (empty($url)) {
    echo json_encode(array('status' => 'error', 'message' => '请输入网站域名'));
    exit;
}

// 智谱AI API配置 - 使用您提供的API Key
$api_key = '47104bcce99c4822974db0bc9ed4f2eb.McDW4LZcouOC8BVv';
$api_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
$model = 'glm-4.5-air'; // 使用您指定的模型

// 构建网站信息
$site_info = "网站域名：{$url}\n";
if (!empty($meta_title)) {
    $site_info .= "网站标题：{$meta_title}\n";
}
if (!empty($meta_keywords)) {
    $site_info .= "Meta关键词：{$meta_keywords}\n";
}
if (!empty($meta_description)) {
    $site_info .= "Meta描述：{$meta_description}\n";
}

// 构建AI提示词
$prompt = "你是一个专业的SEO和网站内容分析专家。请根据以下网站信息，为网站生成合适的关键词和简介：\n\n" .
          $site_info . "\n" .
          "要求：\n" .
          "1. 根据域名和已有信息分析网站可能的主题和功能\n" .
          "2. 生成符合SEO规范的关键词，3-5个，用英文逗号分隔\n" .
          "3. 简介要突出网站的价值和特色，50-200字\n" .
          "4. 内容要符合中文表达习惯\n\n" .
          "请严格按照以下格式输出，不要添加其他内容：\n" .
          "关键词：[生成的关键词，用英文逗号分隔]\n" .
          "简介：[生成的网站简介]";

// 请求智谱AI API
$request_data = array(
    'model' => $model,
    'messages' => array(
        array('role' => 'user', 'content' => $prompt)
    ),
    'temperature' => 0.7
);

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60秒超时
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10秒连接超时
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/json',
    'Authorization: Bearer ' . $api_key
));
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));

$response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 处理cURL错误
if ($curl_error) {
    if (strpos($curl_error, 'timeout') !== false) {
        echo json_encode(array('status' => 'error', 'message' => '生成超时，请重试'));
    } else {
        echo json_encode(array('status' => 'error', 'message' => 'API请求失败：' . $curl_error));
    }
    exit;
}

// 处理HTTP错误
if ($http_code === 524) {
    echo json_encode(array('status' => 'error', 'message' => '生成超时(524)，请重试'));
    exit;
}

if ($http_code !== 200) {
    echo json_encode(array('status' => 'error', 'message' => 'HTTP错误：' . $http_code));
    exit;
}

// 解析AI API的响应
$response_data = json_decode($response, true);
if (isset($response_data['choices'][0]['message']['content'])) {
    $generated_content = $response_data['choices'][0]['message']['content'];
    
    // 解析关键词和简介
    $lines = explode("\n", $generated_content);
    $keywords = '';
    $intro = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, '关键词：') === 0) {
            $keywords = trim(str_replace('关键词：', '', $line));
        } elseif (strpos($line, '简介：') === 0) {
            $intro = trim(str_replace('简介：', '', $line));
        }
    }
    
    // 如果没有按格式解析到内容，尝试其他方式
    if (empty($keywords) && empty($intro)) {
        // 尝试按行分析
        $content_lines = array_filter(array_map('trim', $lines));
        if (count($content_lines) >= 2) {
            $keywords = $content_lines[0];
            $intro = implode(' ', array_slice($content_lines, 1));
        } else {
            $keywords = '';
            $intro = $generated_content;
        }
    }
    
    echo json_encode(array(
        'status' => 'success',
        'message' => '生成成功',
        'keywords' => $keywords,
        'intro' => $intro,
        'raw_content' => $generated_content
    ));
} else {
    echo json_encode(array('status' => 'error', 'message' => 'API响应异常：' . json_encode($response_data)));
}

?>
