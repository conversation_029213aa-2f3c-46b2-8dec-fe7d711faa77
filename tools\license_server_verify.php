<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统授权服务器验证脚本
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 * 
 * 此文件应部署在授权服务器上，用于处理客户端的在线验证请求
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境不显示错误

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(array('status' => 'error', 'message' => 'Method not allowed'));
    exit;
}

// 授权验证类
class LicenseServerVerify {
    
    private $license_key;
    private $db_config;
    
    public function __construct() {
        // 授权密钥（与客户端保持一致）
        $this->license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
        
        // 数据库配置（用于存储授权记录）
        $this->db_config = array(
            'host' => 'localhost',
            'username' => 'license_user',
            'password' => 'license_pass',
            'database' => 'license_db'
        );
    }
    
    /**
     * 处理验证请求
     */
    public function handle_verify_request() {
        try {
            // 获取POST数据
            $input_data = $this->get_input_data();
            
            // 验证请求签名
            if (!$this->verify_request_signature($input_data)) {
                $this->send_response('error', '请求签名验证失败');
                return;
            }
            
            // 验证授权信息
            $verify_result = $this->verify_license_info($input_data);
            
            if ($verify_result['valid']) {
                // 记录验证日志
                $this->log_verify_request($input_data, true);
                
                $this->send_response('valid', '授权验证通过', array(
                    'expire_time' => $verify_result['expire_time'],
                    'license_type' => $verify_result['license_type']
                ));
            } else {
                // 记录验证失败日志
                $this->log_verify_request($input_data, false, $verify_result['reason']);
                
                $this->send_response('invalid', $verify_result['reason']);
            }
            
        } catch (Exception $e) {
            error_log('License verify error: ' . $e->getMessage());
            $this->send_response('error', '服务器内部错误');
        }
    }
    
    /**
     * 获取输入数据
     * @return array
     */
    private function get_input_data() {
        $input = file_get_contents('php://input');
        parse_str($input, $data);
        
        if (empty($data)) {
            $data = $_POST;
        }
        
        return $data;
    }
    
    /**
     * 验证请求签名
     * @param array $data
     * @return bool
     */
    private function verify_request_signature($data) {
        if (!isset($data['verify_signature'])) {
            return false;
        }
        
        $signature = $data['verify_signature'];
        unset($data['verify_signature']);
        
        // 按键名排序
        ksort($data);
        
        // 生成签名字符串
        $sign_string = '';
        foreach ($data as $key => $value) {
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string = rtrim($sign_string, '&');
        
        // 计算期望的签名
        $expected_signature = md5($sign_string . $this->license_key);
        
        return $signature === $expected_signature;
    }
    
    /**
     * 验证授权信息
     * @param array $data
     * @return array
     */
    private function verify_license_info($data) {
        // 连接数据库
        $db = $this->connect_database();
        if (!$db) {
            return array('valid' => false, 'reason' => '数据库连接失败');
        }
        
        // 查询授权记录
        $domain = mysqli_real_escape_string($db, $data['domain']);
        $machine_code = mysqli_real_escape_string($db, $data['machine_code']);
        $version = mysqli_real_escape_string($db, $data['version']);
        
        $sql = "SELECT * FROM licenses WHERE domain = '$domain' AND status = 'active'";
        $result = mysqli_query($db, $sql);
        
        if (!$result || mysqli_num_rows($result) === 0) {
            mysqli_close($db);
            return array('valid' => false, 'reason' => '未找到有效的授权记录');
        }
        
        $license_record = mysqli_fetch_assoc($result);
        
        // 验证到期时间
        if (time() > $license_record['expire_time']) {
            mysqli_close($db);
            return array('valid' => false, 'reason' => '授权已过期');
        }
        
        // 验证机器码（如果设置了）
        if (!empty($license_record['machine_code']) && $license_record['machine_code'] !== $machine_code) {
            mysqli_close($db);
            return array('valid' => false, 'reason' => '机器码不匹配');
        }
        
        // 验证版本（如果设置了）
        if (!empty($license_record['version']) && $license_record['version'] !== '*' && $license_record['version'] !== $version) {
            mysqli_close($db);
            return array('valid' => false, 'reason' => '版本不匹配');
        }
        
        // 更新最后验证时间
        $license_id = $license_record['id'];
        $update_sql = "UPDATE licenses SET last_verify_time = " . time() . " WHERE id = $license_id";
        mysqli_query($db, $update_sql);
        
        mysqli_close($db);
        
        return array(
            'valid' => true,
            'expire_time' => $license_record['expire_time'],
            'license_type' => $license_record['license_type']
        );
    }
    
    /**
     * 连接数据库
     * @return mysqli|false
     */
    private function connect_database() {
        $db = new mysqli(
            $this->db_config['host'],
            $this->db_config['username'],
            $this->db_config['password'],
            $this->db_config['database']
        );
        
        if ($db->connect_error) {
            return false;
        }
        
        $db->set_charset('utf8');
        return $db;
    }
    
    /**
     * 记录验证日志
     * @param array $data
     * @param bool $success
     * @param string $reason
     */
    private function log_verify_request($data, $success, $reason = '') {
        $db = $this->connect_database();
        if (!$db) {
            return;
        }
        
        $domain = mysqli_real_escape_string($db, $data['domain']);
        $machine_code = mysqli_real_escape_string($db, $data['machine_code']);
        $version = mysqli_real_escape_string($db, $data['version']);
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = mysqli_real_escape_string($db, $_SERVER['HTTP_USER_AGENT'] ?? '');
        $status = $success ? 'success' : 'failed';
        $reason = mysqli_real_escape_string($db, $reason);
        $verify_time = time();
        
        $sql = "INSERT INTO verify_logs (domain, machine_code, version, ip_address, user_agent, status, reason, verify_time) 
                VALUES ('$domain', '$machine_code', '$version', '$ip_address', '$user_agent', '$status', '$reason', $verify_time)";
        
        mysqli_query($db, $sql);
        mysqli_close($db);
    }
    
    /**
     * 发送响应
     * @param string $status
     * @param string $message
     * @param array $data
     */
    private function send_response($status, $message, $data = array()) {
        $response = array(
            'status' => $status,
            'message' => $message,
            'timestamp' => time()
        );
        
        if (!empty($data)) {
            $response['data'] = $data;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 处理请求
$verifier = new LicenseServerVerify();
$verifier->handle_verify_request();

/*
数据库表结构示例：

CREATE TABLE `licenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `machine_code` varchar(64) DEFAULT NULL,
  `version` varchar(50) DEFAULT '*',
  `license_type` varchar(50) NOT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `expire_time` int(11) NOT NULL,
  `status` enum('active','suspended','expired') DEFAULT 'active',
  `create_time` int(11) NOT NULL,
  `last_verify_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `domain` (`domain`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `verify_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL,
  `machine_code` varchar(64) DEFAULT NULL,
  `version` varchar(50) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `status` enum('success','failed') NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `verify_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `domain` (`domain`),
  KEY `verify_time` (`verify_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

*/
?>
