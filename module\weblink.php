<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '友情链接';
$pageurl = '?mod=weblink';
$tempfile = 'weblink.html';
$table = $DB->table('weblinks');

$pagesize = 10;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}

// 分页SEO优化 - 在标题中添加页码信息
$seo_title_suffix = '';
if ($curpage > 1) {
	$seo_title_suffix = '(第' . $curpage . '页)';
}
		
$deal_type = intval($_GET['type']);
$cache_id = $deal_type.'-'.$curpage;

if (!$smarty->isCached($tempfile, $cache_id)) {
	$smarty->assign('site_title', $pagename.$seo_title_suffix.' - '.$options['site_name']);
	$smarty->assign('site_keywords', $options['site_keywords']);
	$smarty->assign('site_description', $options['site_description']);
	$smarty->assign('site_path', get_sitepath('weblink'));
	$smarty->assign('site_rss', get_rssfeed());
	
	$where = "l.link_hide=0";
	if ($deal_type > 0) {
		$pageurl .= '&type='.$deal_type;
		if ($deal_type > 0) $where .= " AND l.deal_type=$deal_type";
	}
			
	$results = get_weblink_list($where, 'time', 'DESC', $start, $pagesize);
	$weblinks = array();
	foreach($results as $row) {
		$user = get_one_user($row['user_id']);
		$row['user_qq'] = $user['user_qq'];
		$row['deal_type'] = $deal_types[$row['deal_type']];
		$row['link_price'] = ($row['link_price'] > 0 ? $row['link_price'] : '面议');
		$row['link_time'] = date('Y-m-d', $row['link_time']);
		$row['web_link'] = get_weblink_url($row['link_id']);
		$weblinks[] = $row;
	}
	$total = $DB->get_count($table.' l', $where);
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);
			
	$smarty->assign('pagename', $pagename);
	$smarty->assign('total', $total);
	$smarty->assign('weblinks', $weblinks);
	$smarty->assign('showpage', $showpage);

	// 分页SEO优化 - 计算分页信息
	$total_pages = ceil($total / $pagesize);
	$prev_page = ($curpage > 1) ? $curpage - 1 : 0;
	$next_page = ($curpage < $total_pages) ? $curpage + 1 : 0;

	// 生成分页相关的URL
	$canonical_url = $options['site_url'] . '?mod=weblink';
	if ($deal_type > 0) {
		$canonical_url .= '&type=' . $deal_type;
	}
	if ($curpage > 1) {
		$canonical_url .= '&page=' . $curpage;
	}

	$prev_url = '';
	$next_url = '';
	if ($prev_page > 0) {
		$prev_url = $options['site_url'] . '?mod=weblink';
		if ($deal_type > 0) {
			$prev_url .= '&type=' . $deal_type;
		}
		if ($prev_page > 1) {
			$prev_url .= '&page=' . $prev_page;
		}
	}
	if ($next_page > 0) {
		$next_url = $options['site_url'] . '?mod=weblink';
		if ($deal_type > 0) {
			$next_url .= '&type=' . $deal_type;
		}
		$next_url .= '&page=' . $next_page;
	}

	// 生成robots meta标签 - 第一页允许索引，其他页面noindex但follow
	$robots_content = ($curpage == 1) ? 'index,follow' : 'noindex,follow';

	// 分页SEO相关变量
	$smarty->assign('curpage', $curpage);
	$smarty->assign('total_pages', $total_pages);
	$smarty->assign('canonical_url', $canonical_url);
	$smarty->assign('prev_url', $prev_url);
	$smarty->assign('next_url', $next_url);
	$smarty->assign('robots_content', $robots_content);

	unset($weblinks);
}

smarty_output($tempfile, $cache_id);
?>