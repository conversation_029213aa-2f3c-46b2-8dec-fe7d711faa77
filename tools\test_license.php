<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 授权功能测试工具
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义安全常量
define('IN_HANFOX', true);
define('IN_IWEBDIR', true);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的文件
require(APP_PATH . 'include/function.php');
require(APP_PATH . 'include/security_core.php');
require(APP_PATH . 'include/license.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>授权功能测试</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-item { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #dee2e6; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">授权功能测试</div>
            <p>测试95DIR授权系统的各项功能</p>
        </div>
        
        <?php
        // 测试1：检查授权文件是否存在
        echo '<div class="test-item">';
        echo '<div class="test-title">测试1：检查授权文件</div>';
        
        $license_file = ROOT_PATH . 'data/license.dat';
        if (file_exists($license_file)) {
            $file_size = filesize($license_file);
            echo '<div class="test-result success">✅ 授权文件存在<br>';
            echo '文件路径: ' . $license_file . '<br>';
            echo '文件大小: ' . $file_size . ' 字节</div>';
        } else {
            echo '<div class="test-result error">❌ 授权文件不存在</div>';
        }
        echo '</div>';
        
        // 测试2：测试授权验证器
        echo '<div class="test-item">';
        echo '<div class="test-title">测试2：授权验证器测试</div>';
        
        try {
            $validator = new LicenseValidator();
            $is_valid = $validator->validate();
            
            if ($is_valid) {
                echo '<div class="test-result success">✅ 授权验证通过</div>';
            } else {
                $error_msg = $validator->get_error_message();
                echo '<div class="test-result error">❌ 授权验证失败<br>错误信息: ' . htmlspecialchars($error_msg) . '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="test-result error">❌ 验证器异常: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        echo '</div>';
        
        // 测试3：解密授权文件内容
        echo '<div class="test-item">';
        echo '<div class="test-title">测试3：授权文件内容解析</div>';
        
        if (file_exists($license_file)) {
            $file_content = file_get_contents($license_file);
            $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
            $decrypted_content = authcode($file_content, 'DECODE', $license_key);
            
            if ($decrypted_content) {
                $license_data = json_decode($decrypted_content, true);
                if ($license_data) {
                    echo '<div class="test-result success">✅ 授权文件解密成功<br>';
                    echo '<strong>授权信息:</strong><br>';
                    echo '域名: ' . htmlspecialchars($license_data['domain']) . '<br>';
                    echo '到期时间: ' . date('Y-m-d H:i:s', $license_data['expire_time']) . '<br>';
                    echo '版本: ' . htmlspecialchars($license_data['version']) . '<br>';
                    echo '类型: ' . htmlspecialchars($license_data['license_type']) . '<br>';
                    echo '生成时间: ' . date('Y-m-d H:i:s', $license_data['generate_time']) . '</div>';
                } else {
                    echo '<div class="test-result error">❌ JSON解析失败</div>';
                }
            } else {
                echo '<div class="test-result error">❌ 授权文件解密失败</div>';
            }
        } else {
            echo '<div class="test-result error">❌ 授权文件不存在</div>';
        }
        echo '</div>';
        
        // 测试4：域名验证
        echo '<div class="test-item">';
        echo '<div class="test-title">测试4：域名验证</div>';
        
        $current_domain = '';
        if (isset($_SERVER['HTTP_HOST'])) {
            $current_domain = $_SERVER['HTTP_HOST'];
        } elseif (isset($_SERVER['SERVER_NAME'])) {
            $current_domain = $_SERVER['SERVER_NAME'];
        }
        $current_domain = preg_replace('/:\d+$/', '', $current_domain);
        $current_domain = strtolower($current_domain);
        
        echo '<div class="test-result info">当前域名: ' . htmlspecialchars($current_domain) . '</div>';
        
        if (isset($license_data) && isset($license_data['domain'])) {
            $authorized_domain = $license_data['domain'];
            if ($current_domain === $authorized_domain) {
                echo '<div class="test-result success">✅ 域名验证通过</div>';
            } else {
                echo '<div class="test-result error">❌ 域名验证失败<br>';
                echo '授权域名: ' . htmlspecialchars($authorized_domain) . '<br>';
                echo '当前域名: ' . htmlspecialchars($current_domain) . '</div>';
            }
        }
        echo '</div>';
        
        // 测试5：全局验证函数
        echo '<div class="test-item">';
        echo '<div class="test-title">测试5：全局验证函数</div>';
        
        try {
            $global_result = verify_system_license();
            if ($global_result) {
                echo '<div class="test-result success">✅ 全局授权验证通过</div>';
            } else {
                echo '<div class="test-result error">❌ 全局授权验证失败</div>';
            }
        } catch (Exception $e) {
            echo '<div class="test-result error">❌ 全局验证异常: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="javascript:location.reload()" class="btn">重新测试</a>
            <a href="../index.php" class="btn">返回主站</a>
        </div>
    </div>
</body>
</html>
