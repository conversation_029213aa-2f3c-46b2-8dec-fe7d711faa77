{#include file="header.html"#}

<style>
.push-config { margin: 15px 0; }
.push-config label { display: inline-block; width: 120px; font-weight: bold; }
.push-config input[type="text"] { width: 300px; padding: 5px; }
.push-buttons { margin: 15px 0; }
.push-buttons input[type="submit"] { margin-right: 10px; padding: 8px 15px; }
</style>

<h3 class="title"><em>搜索引擎推送配置</em></h3>
<div style="padding: 15px;">
    <form method="post" action="">
        <div class="push-config">
            <label>百度推送Token：</label>
            <input type="text" name="baidu_token" value="" placeholder="请输入百度站长平台的推送Token" />
            <div style="margin-left: 120px; color: #666; font-size: 12px;">
                获取方式：登录百度站长平台 → 数据引入 → 链接提交 → 主动推送 → 获取Token
            </div>
        </div>
        
        <div class="push-config">
            <label>谷歌API密钥：</label>
            <input type="text" name="google_key" value="" placeholder="请输入Google Search Console API密钥" />
            <div style="margin-left: 120px; color: #666; font-size: 12px;">
                获取方式：Google Cloud Console → APIs & Services → Credentials → Create API Key
            </div>
        </div>
        
        <div class="push-config">
            <label>必应API密钥：</label>
            <input type="text" name="bing_key" value="" placeholder="请输入Bing Webmaster API密钥" />
            <div style="margin-left: 120px; color: #666; font-size: 12px;">
                获取方式：Bing Webmaster Tools → Settings → API Access → Generate API Key
            </div>
        </div>
        
        <div class="push-buttons">
            <input type="submit" name="submit" value="保存配置" class="button" />
        </div>
    </form>
</div>

{#include file="footer.html"#}
