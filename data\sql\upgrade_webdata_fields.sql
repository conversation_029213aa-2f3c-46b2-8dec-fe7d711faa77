-- 升级webdata表字段以支持更大的收录量数值
-- 执行此脚本前请备份数据库
-- 执行方法：在数据库管理工具中运行此SQL脚本

-- 修改web_grank字段（百度收录量）从tinyint改为int
ALTER TABLE `dir_webdata` MODIFY COLUMN `web_grank` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '百度收录量';

-- 修改web_brank字段（必应收录量）从tinyint改为int
ALTER TABLE `dir_webdata` MODIFY COLUMN `web_brank` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '必应收录量';

-- 修改web_srank字段（360收录量）从tinyint改为int
ALTER TABLE `dir_webdata` MODIFY COLUMN `web_srank` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '360收录量';

-- web_arank字段（搜狗收录量）已经是int类型，无需修改
-- ALTER TABLE `dir_webdata` MODIFY COLUMN `web_arank` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '搜狗收录量';

-- 验证字段修改结果
DESCRIBE `dir_webdata`;
