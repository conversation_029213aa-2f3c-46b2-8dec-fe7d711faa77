<?php
/* Smarty version 4.5.5, created on 2025-07-26 19:17:16
  from '/www/wwwroot/www.95dir.com/themes/member/footer.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6884b93c6aad33_65175856',
  'has_nocache_code' => true,
  'file_dependency' => 
  array (
    'c9342660140cb67010e511c324d5f9e96a73204e' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/member/footer.html',
      1 => 1740497631,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6884b93c6aad33_65175856 (Smarty_Internal_Template $_smarty_tpl) {
?>    	</div>
    </div>
    	<div id="footer" class="clearfix">
    	    <?php echo $_smarty_tpl->tpl_vars['site_copyright']->value;?>

    	    <p><?php echo insert_script_time(array(),$_smarty_tpl);?></p>
<!-- 在需要显示的位置添加 -->
<div id="onlineCounter">当前在线：加载中...</div>

<?php echo '<script'; ?>
>
// 定时更新（每60秒）
function updateCounter() {
    fetch('/data/online_stats/online.php')
        .then(response => response.text())
        .then(count => {
            document.getElementById('onlineCounter').innerHTML = 
                `当前在线：${count}人`;
        })
        .catch(() => console.log('统计服务不可用'));
}

// 页面加载时立即执行
updateCounter();
// 设置定时器
setInterval(updateCounter, 60000);
<?php echo '</script'; ?>
>

    </div>
</body>
</html><?php }
}
