<?php
require('common.php');

$fileurl = 'auto_comment_templates.php';
$tempfile = 'auto_comment_templates.html';
$table = $DB->table('auto_comment_templates');

// 获取操作参数
$action = isset($_GET['act']) ? $_GET['act'] : (isset($_POST['act']) ? $_POST['act'] : 'list');

// 分页参数
$pagesize = 20;
$curpage = isset($_GET['page']) ? intval($_GET['page']) : 1;
$start = ($curpage - 1) * $pagesize;

/** list */
if ($action == 'list') {
    $pagetitle = '自动评论模板管理';
    
    $keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));
    $status = isset($_GET['status']) ? intval($_GET['status']) : -1;
    
    $keyurl = !empty($keywords) ? '&keywords='.urlencode($keywords) : '';
    $statusurl = $status >= 0 ? '&status='.$status : '';
    $pageurl = $fileurl.'?'.$keyurl.$statusurl;
    
    $where = '1';
    if ($status >= 0) {
        $where .= " AND status = $status";
    }
    if (!empty($keywords)) {
        $where .= " AND content LIKE '%$keywords%'";
    }
    
    $sql = "SELECT * FROM $table WHERE $where ORDER BY template_id DESC LIMIT $start, $pagesize";
    $query = $DB->query($sql);
    $templates = array();
    
    while ($row = $DB->fetch_array($query)) {
        $row['content_preview'] = mb_strlen($row['content']) > 50 ? mb_substr($row['content'], 0, 50) . '...' : $row['content'];
        $row['status_text'] = $row['status'] == 1 ? '<font color="green">启用</font>' : '<font color="red">禁用</font>';
        $row['create_time_text'] = date('Y-m-d H:i:s', $row['create_time']);
        $row['operate'] = '<a href="'.$fileurl.'?act=edit&template_id='.$row['template_id'].'">编辑</a> | <a href="'.$fileurl.'?act=del&template_id='.$row['template_id'].'" onClick="return confirm(\'确认删除此模板吗？\');">删除</a>';
        $templates[] = $row;
    }
    
    $total = $DB->get_count($table, $where);
    $showpage = showpage($pageurl, $total, $curpage, $pagesize);
    
    $smarty->assign('keywords', $keywords);
    $smarty->assign('status', $status);
    $smarty->assign('templates', $templates);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
    unset($templates);
}

/** add */
if ($action == 'add') {
    $pagetitle = '添加评论模板';
    
    $smarty->assign('h_action', 'saveadd');
    $smarty->assign('status', 1);
}

/** edit */
if ($action == 'edit') {
    $pagetitle = '编辑评论模板';
    
    $template_id = intval($_GET['template_id']);
    $template = $DB->fetch_one("SELECT * FROM $table WHERE template_id = $template_id");
    
    if (!$template) {
        msgbox('评论模板不存在！');
    }
    
    $smarty->assign('template', $template);
    $smarty->assign('h_action', 'saveedit');
}

/** save data */
if (in_array($action, array('saveadd', 'saveedit'))) {
    $content = trim($_POST['content']);
    $category = trim($_POST['category']);
    $status = intval($_POST['status']);
    
    if (empty($content)) {
        msgbox('请输入评论内容！');
    }
    
    if (mb_strlen($content) < 10) {
        msgbox('评论内容至少需要10个字符！');
    }
    
    if (mb_strlen($content) > 500) {
        msgbox('评论内容不能超过500个字符！');
    }
    
    if (empty($category)) {
        $category = 'general';
    }
    
    $data = array(
        'content' => $content,
        'category' => $category,
        'status' => $status,
        'update_time' => time()
    );
    
    if ($action == 'saveadd') {
        // 检查是否重复
        $existing = $DB->fetch_one("SELECT template_id FROM $table WHERE content = '".addslashes($content)."'");
        if ($existing) {
            msgbox('该评论内容已存在！');
        }
        
        $data['create_time'] = time();
        $DB->insert($table, $data);
        
        msgbox('评论模板添加成功！', $fileurl.'?act=add');
    } elseif ($action == 'saveedit') {
        $template_id = intval($_POST['template_id']);
        $where = array('template_id' => $template_id);
        
        $DB->update($table, $data, $where);
        
        msgbox('评论模板编辑成功！', $fileurl);
    }
}

/** del */
if ($action == 'del') {
    $template_ids = (array) ($_POST['template_id'] ? $_POST['template_id'] : $_GET['template_id']);

    // 确保所有ID都是整数
    $template_ids = array_map('intval', $template_ids);
    $template_ids = array_filter($template_ids);

    if (!empty($template_ids)) {
        $ids_str = implode(',', $template_ids);
        $DB->delete($table, "template_id IN ($ids_str)");
    }
    unset($template_ids);

    msgbox('评论模板删除成功！', $fileurl);
}

/** batch operations */
if (in_array($action, array('del', 'enable', 'disable'))) {
    $template_ids = (array) $_POST['template_id'];

    if (empty($template_ids)) {
        msgbox('请选择要操作的模板！');
    }

    // 确保所有ID都是整数
    $template_ids = array_map('intval', $template_ids);
    $template_ids = array_filter($template_ids);

    if (!empty($template_ids)) {
        $ids_str = implode(',', $template_ids);

        switch ($action) {
            case 'enable':
                $DB->update($table, array('status' => 1), "template_id IN ($ids_str)");
                msgbox('批量启用成功！', $fileurl);
                break;
            case 'disable':
                $DB->update($table, array('status' => 0), "template_id IN ($ids_str)");
                msgbox('批量禁用成功！', $fileurl);
                break;
            case 'del':
                $DB->delete($table, "template_id IN ($ids_str)");
                msgbox('批量删除成功！', $fileurl);
                break;
        }
    } else {
        msgbox('请选择有效的模板！');
    }
}

smarty_output($tempfile);
?>
