{#include file="header.html"#}

	{#if $action == 'list'#}
    <h3 class="title"><em>{#$pagetitle#}</em><span><a href="{#$fileurl#}?act=add">+添加模板</a></span></h3>
	<div class="listbox">
		<form name="mform" method="post" action="{#$fileurl#}">
		<div class="search">
			<select name="status" class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='{#$fileurl#}?status='+this.options[this.selectedIndex].value;}">
				<option value="-1"{#opt_selected($status, -1)#}>全部状态</option>
				<option value="1"{#opt_selected($status, 1)#}>已启用</option>
				<option value="0"{#opt_selected($status, 0)#}>已禁用</option>
			</select>
			<input name="keywords" type="text" id="keywords" class="ipt" size="30" value="{#$keywords#}" />
			<input type="submit" class="btn" value="搜索" />
        </div>
        </form>

		<form name="mform" method="post" action="{#$fileurl#}">
		<div class="toolbar">
			<select name="act" id="act" class="sel">
			<option value="del" style="color: #f00;">删除选定</option>
            <option value="enable" style="color: #06c;">启用选定</option>
            <option value="disable" style="color: #f60;">禁用选定</option>
			</select>
			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('template_id[]')==false){alert('请指定您要操作的模板ID！');return false;}else{return confirm('确认执行此操作吗？');}">
		</div>

    	<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>
				<th>ID</th>
				<th>评论内容</th>
				<th>分类</th>
				<th>状态</th>
				<th>创建时间</th>
				<th>操作选项</th>
			</tr>
			{#foreach from=$templates item=template#}
			<tr>
				<td><input name="template_id[]" type="checkbox" value="{#$template.template_id#}"></td>
				<td>{#$template.template_id#}</td>
				<td class="ltext">{#$template.content_preview#}</td>
				<td>{#$template.category#}</td>
				<td>{#$template.status_text#}</td>
				<td>{#$template.create_time_text#}</td>
				<td>{#$template.operate#}</td>
			</tr>
			{#/foreach#}
		</table>

		<div class="page">{#$showpage#}</div>
		</form>
	</div>
	{#/if#}
	{#if $action == 'add' || $action == 'edit'#}
    <h3 class="title"><em>{#$pagetitle#}</em></h3>
    <div class="formbox">
        <form name="mform" method="post" action="{#$fileurl#}?act={#$h_action#}">
			{#if $action == 'edit'#}
			<input type="hidden" name="template_id" value="{#$template.template_id#}">
			{#/if#}
			<table width="100%" border="0" cellspacing="1" cellpadding="0">
				<tr>
					<th>评论内容：</th>
					<td><textarea name="content" rows="4" cols="60" class="ipt">{#if $template#}{#$template.content|escape#}{#/if#}</textarea><span class="tips">请输入评论内容，10-500字符</span></td>
				</tr>
				<tr>
					<th>分类：</th>
					<td>
						<select name="category" class="sel">
							<option value="general"{#opt_selected($template.category, 'general')#}>通用评论</option>
							<option value="design"{#opt_selected($template.category, 'design')#}>设计相关</option>
							<option value="content"{#opt_selected($template.category, 'content')#}>内容相关</option>
							<option value="function"{#opt_selected($template.category, 'function')#}>功能相关</option>
							<option value="service"{#opt_selected($template.category, 'service')#}>服务相关</option>
						</select>
					</td>
				</tr>
				<tr>
					<th>状态：</th>
					<td>
						<input name="status" type="radio" id="status_1" value="1"{#opt_checked($template.status, 1)#}><label for="status_1">启用</label>
						<input name="status" type="radio" id="status_0" value="0"{#opt_checked($template.status, 0)#}><label for="status_0">禁用</label>
					</td>
				</tr>
        	</table>
        	<div class="btnbox">
				<input name="act" type="hidden" id="act" value="{#$h_action#}">
				<input type="submit" class="btn" value="保 存">
				<input type="button" class="btn" value="返回列表" onClick="location='{#$fileurl#}'">
			</div>
        </form>
    </div>
	{#/if#}
</div>


{#include file="footer.html"#}
