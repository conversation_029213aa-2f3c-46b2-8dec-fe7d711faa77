<?php
/*
 * <AUTHOR> 95DIR系统
 * @Description  : 95DIR分类目录系统健康检查工具
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * 用于检查系统清理后是否正常运行
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR系统健康检查</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .check-item { margin: 15px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #ddd; }
        .check-success { background: #d4edda; border-left-color: #28a745; color: #155724; }
        .check-warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
        .check-error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; }
        .check-title { font-weight: bold; margin-bottom: 5px; }
        .check-detail { font-size: 14px; margin-left: 20px; }
        .file-list { font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .summary { background: #e9ecef; padding: 20px; border-radius: 6px; margin-top: 30px; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR系统健康检查</div>
            <p>检查系统清理后的运行状态</p>
        </div>
        
        <?php
        $checks = array();
        $total_checks = 0;
        $passed_checks = 0;
        
        // 检查1：核心文件存在性
        $total_checks++;
        $core_files = array(
            'source/init.php' => '系统初始化文件',
            'source/version.php' => '版本信息文件',
            'source/include/license.php' => '授权验证模块',
            'source/include/anti_debug.php' => '反调试模块',
            'source/include/function.php' => '核心函数库',
            'source/include/mysql.php' => '数据库类',
            'index.php' => '系统入口文件',
            'config.php' => '配置文件'
        );
        
        $missing_files = array();
        foreach ($core_files as $file => $desc) {
            if (!file_exists(ROOT_PATH . $file)) {
                $missing_files[] = "$file ($desc)";
            }
        }
        
        if (empty($missing_files)) {
            $checks[] = array('type' => 'success', 'title' => '核心文件检查', 'detail' => '所有核心文件都存在');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'error', 'title' => '核心文件检查', 'detail' => '缺少文件：' . implode(', ', $missing_files));
        }
        
        // 检查2：授权系统文件
        $total_checks++;
        $license_files = array(
            'system/license_manager.php' => '授权管理后台',
            'system/templates/license_manager.html' => '授权管理模板',
            'tools/license_generator.php' => '授权生成工具',
            'tools/license_test.php' => '授权测试工具',
            'tools/create_demo_license.php' => '演示授权工具'
        );
        
        $missing_license_files = array();
        foreach ($license_files as $file => $desc) {
            if (!file_exists(ROOT_PATH . $file)) {
                $missing_license_files[] = "$file ($desc)";
            }
        }
        
        if (empty($missing_license_files)) {
            $checks[] = array('type' => 'success', 'title' => '授权系统文件检查', 'detail' => '所有授权系统文件都存在');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'error', 'title' => '授权系统文件检查', 'detail' => '缺少文件：' . implode(', ', $missing_license_files));
        }
        
        // 检查3：系统初始化
        $total_checks++;
        try {
            define('IN_HANFOX', true);
            require(APP_PATH . 'init.php');
            $checks[] = array('type' => 'success', 'title' => '系统初始化', 'detail' => '系统初始化成功');
            $passed_checks++;
        } catch (Exception $e) {
            $checks[] = array('type' => 'error', 'title' => '系统初始化', 'detail' => '初始化失败：' . $e->getMessage());
        }
        
        // 检查4：数据库连接
        $total_checks++;
        if (isset($DB) && is_object($DB)) {
            try {
                $version = $DB->version();
                $checks[] = array('type' => 'success', 'title' => '数据库连接', 'detail' => "数据库连接正常，MySQL版本：$version");
                $passed_checks++;
            } catch (Exception $e) {
                $checks[] = array('type' => 'error', 'title' => '数据库连接', 'detail' => '数据库连接失败：' . $e->getMessage());
            }
        } else {
            $checks[] = array('type' => 'error', 'title' => '数据库连接', 'detail' => '数据库对象不存在');
        }
        
        // 检查5：授权验证功能
        $total_checks++;
        if (function_exists('verify_system_license')) {
            $checks[] = array('type' => 'success', 'title' => '授权验证功能', 'detail' => '授权验证函数存在');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'error', 'title' => '授权验证功能', 'detail' => '授权验证函数不存在');
        }
        
        // 检查6：反调试功能
        $total_checks++;
        if (function_exists('anti_debug_check')) {
            $checks[] = array('type' => 'success', 'title' => '反调试功能', 'detail' => '反调试函数存在');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'warning', 'title' => '反调试功能', 'detail' => '反调试函数不存在（可能未启用）');
            $passed_checks++; // 这个不算错误
        }
        
        // 检查7：模板系统
        $total_checks++;
        if (isset($smarty) && is_object($smarty)) {
            $checks[] = array('type' => 'success', 'title' => '模板系统', 'detail' => 'Smarty模板引擎加载正常');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'error', 'title' => '模板系统', 'detail' => 'Smarty模板引擎未加载');
        }
        
        // 检查8：目录权限
        $total_checks++;
        $writable_dirs = array('data', 'uploads', 'data/cache', 'data/compile');
        $permission_issues = array();
        
        foreach ($writable_dirs as $dir) {
            $dir_path = ROOT_PATH . $dir;
            if (!is_dir($dir_path)) {
                $permission_issues[] = "$dir (目录不存在)";
            } elseif (!is_writable($dir_path)) {
                $permission_issues[] = "$dir (不可写)";
            }
        }
        
        if (empty($permission_issues)) {
            $checks[] = array('type' => 'success', 'title' => '目录权限检查', 'detail' => '所有必要目录权限正常');
            $passed_checks++;
        } else {
            $checks[] = array('type' => 'warning', 'title' => '目录权限检查', 'detail' => '权限问题：' . implode(', ', $permission_issues));
        }
        
        // 显示检查结果
        foreach ($checks as $check) {
            echo '<div class="check-item check-' . $check['type'] . '">';
            echo '<div class="check-title">' . $check['title'] . '</div>';
            echo '<div class="check-detail">' . $check['detail'] . '</div>';
            echo '</div>';
        }
        
        // 显示总结
        $success_rate = round(($passed_checks / $total_checks) * 100, 1);
        $summary_class = $success_rate >= 90 ? 'success' : ($success_rate >= 70 ? 'warning' : 'error');
        ?>
        
        <div class="summary">
            <h3>检查总结</h3>
            <p><strong>总检查项：</strong><?php echo $total_checks; ?></p>
            <p><strong>通过检查：</strong><?php echo $passed_checks; ?></p>
            <p><strong>成功率：</strong><?php echo $success_rate; ?>%</p>
            
            <?php if ($success_rate >= 90): ?>
                <p style="color: #28a745;"><strong>✅ 系统状态良好，可以正常使用！</strong></p>
            <?php elseif ($success_rate >= 70): ?>
                <p style="color: #ffc107;"><strong>⚠️ 系统基本正常，但有一些问题需要注意。</strong></p>
            <?php else: ?>
                <p style="color: #dc3545;"><strong>❌ 系统存在严重问题，需要修复后才能使用。</strong></p>
            <?php endif; ?>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="license_test.php" class="btn">测试授权系统</a>
            <a href="license_generator.php" class="btn">生成授权文件</a>
            <a href="../index.php" class="btn">访问网站首页</a>
            <a href="../system/license_manager.php" class="btn">授权管理后台</a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>系统清理说明：</h4>
            <ul>
                <li>已删除所有测试文件和调试文件</li>
                <li>已删除备份文件和重复文件</li>
                <li>已删除临时文件和缓存文件</li>
                <li>保留了所有核心功能文件</li>
                <li>授权保护系统完整保留</li>
            </ul>
            
            <p><strong>注意：</strong>如果发现任何问题，请检查文件权限和配置设置。</p>
        </div>
    </div>
</body>
</html>
