<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-11 08:56:39
 * @LastEditTime : 2025-02-14 14:50:41
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\member\module\website.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
if (!defined('IN_HANFOX')) exit('Access Denied');

require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

$pageurl = '?mod=website';
$tplfile = 'website.html';
$table = $DB->table('websites');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$pagename = '网站管理';
		$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
		
		$pagesize = 10;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "w.user_id=".$myself['user_id'];
	
		$websites = get_website_list($where, 'ctime', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' w', $where);
		$showpage = showpage($pageurl, $total, $curpage, $pagesize);
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('websites', $websites);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** add */
	if ($action == 'add') {
		$pagename = '网站提交';
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
		$smarty->assign('category_option', get_category_option('webdir', 0, 0, 0));	
		$smarty->assign('do', 'saveadd');
	}
	
	/** edit */
	if ($action == 'edit') {
		$pagename = '网站编辑';
		
		$web_id = intval($_GET['wid']);
		$where = "w.user_id=$myself[user_id] AND w.web_id=$web_id";
		$web = get_one_website($where);
		if (!$web) {
			msgbox('您要修改的内容不存在或无权限！');
		}
		$web['web_ip'] = long2ip($web['web_ip']);
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('site_title', $pagename.' - '.$options['site_title']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);	
		$smarty->assign('category_option', get_category_option('webdir', 0, $web['cate_id'], 0));
		$smarty->assign('web', $web);	
		$smarty->assign('do', 'saveedit');
	}
	
	/** save */
	if (in_array($_POST['do'], array('saveadd', 'saveedit'))) {
		$cate_id = intval($_POST['cate_id']);
		$web_name = trim($_POST['web_name']);
		$web_url = trim($_POST['web_url']);
		$web_tags = trim($_POST['web_tags']);
		$web_intro = trim($_POST['web_intro']);
		$web_ip = trim($_POST['web_ip']);
		$web_grank = intval($_POST['web_grank']);
		$web_brank = intval($_POST['web_brank']);
		$web_srank = intval($_POST['web_srank']);
		$web_arank = intval($_POST['web_arank']);
		$web_time = time();
		
		if ($cate_id <= 0) {
			msgbox('请选择网站所属分类！');
		} else {
			$cate = get_one_category($cate_id);
			if ($cate['cate_childcount'] > 0) {
				msgbox('指定的分类下有子分类，请选择子分类进行操作！');
			}
		}
	
		if (empty($web_name)) {
			msgbox('请输入网站名称！');
		} else {
			// 检查网站名称长度（中文算2个字符）
			$name_length = 0;
			for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
				$char = mb_substr($web_name, $i, 1, 'UTF-8');
				if (ord($char) > 127) {
					$name_length += 2; // 中文字符算2个字符
				} else {
					$name_length += 1; // 英文字符算1个字符
				}
			}

			if ($name_length > 12) {
				msgbox('网站名称过长！最多12个字符（6个汉字）');
			}

			if (!censor_words($options['filter_words'], $web_name)) {
				msgbox('网站名称中含有非法关键词！');
			}
		}
		
		if (empty($web_url)) {
			msgbox('请输入网站域名！');
		} else {
			if (!is_valid_domain($web_url)) {
				msgbox('请输入正确的网站域名！');
			}
		}
		
		if (!empty($web_tags)) {
			if (!censor_words($options['filter_words'], $web_tags)) {
				msgbox('TAG标签中含有非法关键词！');
			}
			
			$web_tags = str_replace('，', ',', $web_tags);
			$web_tags = str_replace(',,', ',', $web_tags);
			if (substr($web_tags, -1) == ',') {
				$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
			}
		}
			
		if (empty($web_intro)) {
			msgbox('请输入网站简介！');
		} else {
			if (!censor_words($options['filter_words'], $web_intro)) {
				msgbox('网站简介中含有非法关键词！');	
			}
		}
		
		$web_ip = sprintf("%u", ip2long($web_ip));
		
		$web_data = array(
			'cate_id' => $cate_id,
			'user_id' => $myself['user_id'],
			'web_name' => $web_name,
			'web_url' => $web_url,
			'web_tags' => $web_tags,
			'web_intro' => $web_intro,
			'web_status' => 2,
			'web_ctime' => $web_time,
		);
		
		if ($_POST['do'] == 'saveadd') {
    		$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
    		if ($DB->num_rows($query)) {
    			$existing_web = $DB->fetch_array($query);
    			$status_msg = '';

    			switch($existing_web['web_status']) {
    				case 1:
    					$status_msg = '该网站已被拉黑，无法重复提交！';
    					break;
    				case 2:
    					$status_msg = '该网站正在审核中，请勿重复提交！';
    					break;
    				case 3:
    					$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），请勿重复提交！';
    					break;
    				case 4:
    					$status_msg = '该网站审核不通过，请修改后重新提交！';
    					break;
    				default:
    					$status_msg = '您所提交的网站已存在！';
    			}

        		msgbox($status_msg);
    		}
			$DB->insert($table, $web_data);
			$insert_id = $DB->insert_id();
			
			$stat_data = array(
				'web_id' => $insert_id,
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->insert($DB->table('webdata'), $stat_data);
            $options['wechat_robot'] ? send_msg($web_data,$options,'wechat_robot') : '';
            $options['dingding_robot'] ? send_msg($web_data,$options,'dingding_robot') : '';

            // 发送邮件通知管理员
            send_admin_email_notification($web_data, $options, '提交');

			msgbox('网站提交成功！', $pageurl);
		} elseif ($_POST['do'] == 'saveedit') {
			$web_id = intval($_POST['web_id']);
			$where = array('web_id' => $web_id);
			$DB->update($table, $web_data, $where);
			
			$stat_data = array(
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->update($DB->table('webdata'), $stat_data, $where);
			$options['wechat_robot'] ? send_msg($web_data,$options,'wechat_robot') : '';
            $options['dingding_robot'] ? send_msg($web_data,$options,'dingding_robot') : '';

            // 发送邮件通知管理员
            send_admin_email_notification($web_data, $options, '编辑');

			msgbox('网站编辑成功！', $pageurl);
		}
	}
}

// 发送审核信息
function send_msg($web_data, $options, $type='wechat_robot'){
    $action = $_POST['do'] == 'saveadd' ? '新站点' : '编辑站点';
    $messageData = [
        "msgtype" => "text",
        "text" => [
            "content" => "有".$action."需要审核\n站点名称：".$web_data['web_name']."\n站点地址：".$web_data['web_url']."\n提交时间：".date('Y-m-d H:i:s',$web_data['web_ctime']),
        ]
    ];
    $jsonData = json_encode($messageData);
    $ch = curl_init($type == 'wechat_robot' ? $options['wechat_robot'] : $options['dingding_robot']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json; charset=utf-8',
        'Content-Length: ' . strlen($jsonData)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
}

// 发送管理员邮件通知
function send_admin_email_notification($web_data, $options, $action_type) {
    global $smarty, $myself, $DB;

    // 记录调试信息
    $debug_log = "=== 邮件发送调试 " . date('Y-m-d H:i:s') . " ===\n";
    $debug_log .= "Action Type: " . $action_type . "\n";
    $debug_log .= "User ID: " . $myself['user_id'] . "\n";
    $debug_log .= "Web Name: " . $web_data['web_name'] . "\n";
    $debug_log .= "Web URL: " . $web_data['web_url'] . "\n";

    // 检查邮件配置是否完整
    $debug_log .= "SMTP Host: " . ($options['smtp_host'] ? $options['smtp_host'] : 'EMPTY') . "\n";
    $debug_log .= "SMTP Port: " . ($options['smtp_port'] ? $options['smtp_port'] : 'EMPTY') . "\n";
    $debug_log .= "SMTP Auth: " . ($options['smtp_auth'] ? $options['smtp_auth'] : 'EMPTY') . "\n";
    $debug_log .= "SMTP User: " . ($options['smtp_user'] ? $options['smtp_user'] : 'EMPTY') . "\n";
    $debug_log .= "SMTP Pass: " . ($options['smtp_pass'] ? 'SET' : 'EMPTY') . "\n";
    $debug_log .= "Admin Email: " . ($options['admin_email'] ? $options['admin_email'] : 'EMPTY') . "\n";

    if (empty($options['smtp_host']) || empty($options['smtp_port']) ||
        empty($options['smtp_auth']) || empty($options['smtp_user']) ||
        empty($options['smtp_pass']) || empty($options['admin_email'])) {
        $debug_log .= "配置检查失败：邮件配置不完整\n";
        error_log($debug_log, 3, ROOT_PATH . "data/email_debug.log");
        return false;
    }

    $debug_log .= "配置检查通过\n";

    require(APP_PATH.'include/sendmail.php');

    // 获取用户信息
    $user_info = $DB->fetch_one("SELECT user_email FROM ".$DB->table('users')." WHERE user_id=".$myself['user_id']);
    $debug_log .= "用户邮箱: " . ($user_info['user_email'] ? $user_info['user_email'] : 'EMPTY') . "\n";

    // 准备邮件模板变量
    $smarty->assign('site_name', $options['site_name']);
    $smarty->assign('site_url', $options['site_url']);
    $smarty->assign('action_type', $action_type);
    $smarty->assign('web_name', $web_data['web_name']);
    $smarty->assign('web_url', $web_data['web_url']);
    $smarty->assign('web_intro', $web_data['web_intro']);
    $smarty->assign('user_email', $user_info['user_email']);
    $smarty->assign('submit_time', date('Y-m-d H:i:s', $web_data['web_ctime']));

    // 检查邮件模板是否存在
    $template_path = ROOT_PATH . 'themes/system/admin_notify_mail.html';
    if (!file_exists($template_path)) {
        $debug_log .= "邮件模板不存在: " . $template_path . "\n";
        error_log($debug_log, 3, ROOT_PATH . "data/email_debug.log");
        return false;
    }
    $debug_log .= "邮件模板存在\n";

    // 生成邮件内容
    $mailbody = $smarty->fetch('admin_notify_mail.html');
    $debug_log .= "邮件内容长度: " . strlen($mailbody) . "\n";

    // 发送邮件给管理员
    $subject = '['.$options['site_name'].'] 有新的网站'.$action_type.'需要审核';
    $debug_log .= "邮件主题: " . $subject . "\n";
    $debug_log .= "收件人: " . $options['admin_email'] . "\n";

    $result = sendmail($options['admin_email'], $subject, $mailbody);

    $debug_log .= "发送结果: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
    $debug_log .= "=== 调试结束 ===\n\n";

    // 记录调试日志
    error_log($debug_log, 3, ROOT_PATH . "data/email_debug.log");

    return $result;
}

smarty_output($tplfile);
?>