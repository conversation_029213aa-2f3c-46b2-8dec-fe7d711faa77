<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 快速授权工具 - 为指定域名快速创建授权
 * @Version      : 1.0
 * @Date         : 2025-08-07
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 加载必要的文件
require(APP_PATH . 'include/function.php');

// 处理授权请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_license'])) {
    $domain = trim($_POST['domain']);
    $expire_days = intval($_POST['expire_days']);
    $license_type = trim($_POST['license_type']);
    
    if (empty($domain)) {
        $error = "请输入域名";
    } else {
        // 创建授权信息
        $license_info = array(
            'domain' => $domain,
            'expire_time' => time() + ($expire_days * 24 * 60 * 60),
            'version' => '*', // 支持所有版本
            'license_type' => $license_type,
            'customer_name' => '快速授权用户',
            'customer_email' => '<EMAIL>',
            'machine_code' => '', // 不绑定机器码
            'generate_time' => time(),
            'generator' => 'QuickAuthorize v1.0'
        );
        
        // 生成签名
        $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
        
        $sign_data = $license_info;
        ksort($sign_data);
        $sign_string = '';
        foreach ($sign_data as $key => $value) {
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string = rtrim($sign_string, '&');
        $license_info['signature'] = md5($sign_string . $license_key);
        
        // 转换为JSON并加密
        $json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);
        $encrypted_data = authcode($json_data, 'ENCODE', $license_key);
        
        // 保存授权文件
        $license_file = ROOT_PATH . 'data/license_' . str_replace('.', '_', $domain) . '.dat';
        
        // 确保目录存在
        $data_dir = ROOT_PATH . 'data/';
        if (!is_dir($data_dir)) {
            mkdir($data_dir, 0755, true);
        }
        
        // 保存文件
        if (file_put_contents($license_file, $encrypted_data)) {
            $success = "授权文件创建成功！";
            $download_file = $license_file;
        } else {
            $error = "授权文件保存失败！";
        }
    }
}

// 处理下载请求
if (isset($_GET['download']) && isset($_GET['file'])) {
    $file = ROOT_PATH . 'data/' . basename($_GET['file']);
    if (file_exists($file)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="license.dat"');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR快速授权工具</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .btn { background: #007bff; color: #fff; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .help-text { font-size: 12px; color: #666; margin-top: 5px; }
        .quick-buttons { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px; }
        .quick-btn { padding: 10px; background: #e9ecef; border: 1px solid #ced4da; border-radius: 4px; cursor: pointer; text-align: center; }
        .quick-btn:hover { background: #dee2e6; }
        .quick-btn.active { background: #007bff; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR快速授权工具</div>
            <p>为指定域名快速创建授权文件</p>
        </div>
        
        <?php if (isset($success)): ?>
        <div class="alert alert-success">
            <h4>✅ <?php echo $success; ?></h4>
            <p>域名：<?php echo htmlspecialchars($domain); ?></p>
            <p>到期时间：<?php echo date('Y-m-d H:i:s', $license_info['expire_time']); ?></p>
            <p>授权类型：<?php echo htmlspecialchars($license_type); ?></p>
            <div style="text-align: center; margin-top: 15px;">
                <a href="?download=1&file=<?php echo basename($download_file); ?>" class="btn btn-success">下载授权文件 (license.dat)</a>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-error">
            <h4>❌ 错误</h4>
            <p><?php echo htmlspecialchars($error); ?></p>
        </div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label for="domain">目标域名 *</label>
                <input type="text" id="domain" name="domain" required placeholder="例如：test.95dir.com" value="<?php echo isset($_POST['domain']) ? htmlspecialchars($_POST['domain']) : 'test.95dir.com'; ?>">
                <div class="help-text">输入需要授权的域名</div>
            </div>
            
            <div class="form-group">
                <label>快速选择有效期</label>
                <div class="quick-buttons">
                    <div class="quick-btn" onclick="setExpireDays(30)">30天</div>
                    <div class="quick-btn" onclick="setExpireDays(90)">90天</div>
                    <div class="quick-btn" onclick="setExpireDays(180)">180天</div>
                    <div class="quick-btn active" onclick="setExpireDays(365)">1年</div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="expire_days">有效期（天数）*</label>
                <input type="number" id="expire_days" name="expire_days" required value="365" min="1" max="3650">
                <div class="help-text">授权有效天数</div>
            </div>
            
            <div class="form-group">
                <label for="license_type">授权类型 *</label>
                <select id="license_type" name="license_type" required>
                    <option value="developer">开发者授权</option>
                    <option value="trial">试用授权</option>
                    <option value="personal">个人授权</option>
                    <option value="commercial">商业授权</option>
                </select>
                <div class="help-text">选择授权类型</div>
            </div>
            
            <div class="form-group">
                <button type="submit" name="create_license" class="btn">创建授权文件</button>
            </div>
        </form>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>使用说明：</h4>
            <ol>
                <li>输入目标域名（如：test.95dir.com）</li>
                <li>选择授权有效期</li>
                <li>点击"创建授权文件"</li>
                <li>下载生成的 license.dat 文件</li>
                <li>将文件上传到目标站点的 data/ 目录</li>
            </ol>
            
            <h4>部署步骤：</h4>
            <ol>
                <li>下载授权文件</li>
                <li>上传到 test.95dir.com/data/license.dat</li>
                <li>访问 test.95dir.com/tools/license_test.php 验证</li>
            </ol>
        </div>
    </div>
    
    <script>
        function setExpireDays(days) {
            document.getElementById('expire_days').value = days;
            
            // 更新按钮状态
            document.querySelectorAll('.quick-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
