<?php
/*
 * <AUTHOR> 95DIR调试工具
 * @Description  : 调试工具，检查系统错误
 * @Version      : 1.0
 * @Date         : 2025-08-07
 */

// 开启所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>95DIR系统调试工具</h1>";

// 检查基本信息
echo "<h2>基本信息</h2>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "当前目录: " . __DIR__ . "<br>";
echo "脚本路径: " . __FILE__ . "<br>";

// 定义路径
$root_path = str_replace('\\', '/', dirname(dirname(__FILE__))).'/';
$app_path = $root_path.'source/';

echo "ROOT_PATH: " . $root_path . "<br>";
echo "APP_PATH: " . $app_path . "<br>";

// 检查目录是否存在
echo "<h2>目录检查</h2>";
echo "ROOT_PATH 存在: " . (is_dir($root_path) ? '是' : '否') . "<br>";
echo "APP_PATH 存在: " . (is_dir($app_path) ? '是' : '否') . "<br>";
echo "source/include 存在: " . (is_dir($app_path . 'include') ? '是' : '否') . "<br>";

// 检查关键文件
echo "<h2>文件检查</h2>";
$files_to_check = array(
    'source/include/function.php',
    'source/version.php',
    'source/include/security_core.php',
    'source/include/integrity_guard.php',
    'source/include/license.php',
    'data/license.dat'
);

foreach ($files_to_check as $file) {
    $full_path = $root_path . $file;
    $exists = file_exists($full_path);
    $readable = $exists ? is_readable($full_path) : false;
    $size = $exists ? filesize($full_path) : 0;
    
    echo $file . ": ";
    echo "存在=" . ($exists ? '是' : '否') . ", ";
    echo "可读=" . ($readable ? '是' : '否') . ", ";
    echo "大小=" . $size . "字节<br>";
}

// 尝试加载文件
echo "<h2>文件加载测试</h2>";

// 定义常量
if (!defined('ROOT_PATH')) define('ROOT_PATH', $root_path);
if (!defined('APP_PATH')) define('APP_PATH', $app_path);
if (!defined('IN_HANFOX')) define('IN_HANFOX', true);

// 测试加载 function.php
echo "<h3>加载 function.php</h3>";
try {
    if (file_exists($app_path . 'include/function.php')) {
        require_once($app_path . 'include/function.php');
        echo "✅ function.php 加载成功<br>";
        
        // 测试 authcode 函数
        if (function_exists('authcode')) {
            echo "✅ authcode 函数存在<br>";
            $test = authcode('test', 'ENCODE', 'key');
            echo "✅ authcode 测试成功，结果长度: " . strlen($test) . "<br>";
        } else {
            echo "❌ authcode 函数不存在<br>";
        }
    } else {
        echo "❌ function.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ function.php 加载失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ function.php 致命错误: " . $e->getMessage() . "<br>";
}

// 测试加载 version.php
echo "<h3>加载 version.php</h3>";
try {
    if (file_exists($app_path . 'version.php')) {
        require_once($app_path . 'version.php');
        echo "✅ version.php 加载成功<br>";
        if (defined('SYS_VERSION')) {
            echo "✅ SYS_VERSION 常量: " . SYS_VERSION . "<br>";
        } else {
            echo "❌ SYS_VERSION 常量未定义<br>";
        }
    } else {
        echo "❌ version.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ version.php 加载失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ version.php 致命错误: " . $e->getMessage() . "<br>";
}

// 测试加载 security_core.php
echo "<h3>加载 security_core.php</h3>";
try {
    if (file_exists($app_path . 'include/security_core.php')) {
        require_once($app_path . 'include/security_core.php');
        echo "✅ security_core.php 加载成功<br>";
        
        if (class_exists('SecurityCore')) {
            echo "✅ SecurityCore 类存在<br>";
            try {
                $security = SecurityCore::getInstance();
                echo "✅ SecurityCore 实例化成功<br>";
                
                $key = $security->generate_dynamic_key('test');
                echo "✅ 动态密钥生成成功，长度: " . strlen($key) . "<br>";
            } catch (Exception $e) {
                echo "❌ SecurityCore 使用失败: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ SecurityCore 类不存在<br>";
        }
    } else {
        echo "❌ security_core.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ security_core.php 加载失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ security_core.php 致命错误: " . $e->getMessage() . "<br>";
}

// 测试加载 integrity_guard.php
echo "<h3>加载 integrity_guard.php</h3>";
try {
    if (file_exists($app_path . 'include/integrity_guard.php')) {
        require_once($app_path . 'include/integrity_guard.php');
        echo "✅ integrity_guard.php 加载成功<br>";
        
        if (class_exists('IntegrityGuard')) {
            echo "✅ IntegrityGuard 类存在<br>";
            try {
                $guard = IntegrityGuard::getInstance();
                echo "✅ IntegrityGuard 实例化成功<br>";
            } catch (Exception $e) {
                echo "❌ IntegrityGuard 使用失败: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ IntegrityGuard 类不存在<br>";
        }
    } else {
        echo "❌ integrity_guard.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ integrity_guard.php 加载失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ integrity_guard.php 致命错误: " . $e->getMessage() . "<br>";
}

// 测试加载 license.php
echo "<h3>加载 license.php</h3>";
try {
    if (file_exists($app_path . 'include/license.php')) {
        require_once($app_path . 'include/license.php');
        echo "✅ license.php 加载成功<br>";
        
        if (class_exists('LicenseValidator')) {
            echo "✅ LicenseValidator 类存在<br>";
            try {
                $validator = new LicenseValidator();
                echo "✅ LicenseValidator 实例化成功<br>";
                
                $machine_code = $validator->generate_machine_code();
                echo "✅ 机器码生成成功: " . $machine_code . "<br>";
            } catch (Exception $e) {
                echo "❌ LicenseValidator 使用失败: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ LicenseValidator 类不存在<br>";
        }
        
        // 检查函数
        $functions = array('verify_system_license', 'quick_license_check', 'advanced_security_check', 'random_security_verify');
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "✅ 函数 $func 存在<br>";
            } else {
                echo "❌ 函数 $func 不存在<br>";
            }
        }
    } else {
        echo "❌ license.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ license.php 加载失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ license.php 致命错误: " . $e->getMessage() . "<br>";
}

// 检查内存使用
echo "<h2>系统信息</h2>";
echo "内存使用: " . memory_get_usage(true) . " 字节<br>";
echo "内存峰值: " . memory_get_peak_usage(true) . " 字节<br>";
echo "已加载的扩展: " . implode(', ', get_loaded_extensions()) . "<br>";

echo "<h2>测试完成</h2>";
echo "<p><a href='simple_security_test.php'>简化安全测试</a> | <a href='advanced_security_test.php'>高级安全测试</a></p>";

?>
