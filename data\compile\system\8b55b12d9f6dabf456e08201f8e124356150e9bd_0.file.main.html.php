<?php
/* Smarty version 4.5.5, created on 2025-07-26 19:18:09
  from '/www/wwwroot/www.95dir.com/themes/system/main.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6884b9714f0301_37446608',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '8b55b12d9f6dabf456e08201f8e124356150e9bd' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/main.html',
      1 => 1740145545,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6884b9714f0301_37446608 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

    <h3 class="title"><em>用户登录信息</em></h3>
	<div style="padding: 15px 10px;"><?php echo $_smarty_tpl->tpl_vars['myself']->value['user_email'];?>
，登陆时间：<?php echo $_smarty_tpl->tpl_vars['myself']->value['login_time'];?>
　登陆IP：<?php echo $_smarty_tpl->tpl_vars['myself']->value['login_ip'];?>
　登陆次数： <?php echo $_smarty_tpl->tpl_vars['myself']->value['login_count'];?>
 次</div>
               
	<h3 class="title"><em>站内数据统计</em></h3>
    <div style="padding: 10px;">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr height="30">
				<td width="50%">网站广告：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['adver'];?>
</b>　-　<a href="adver.php">快速管理&raquo;</a></td>
				<td width="50%">友情链接：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['link'];?>
</b>　-　<a href="link.php">快速管理&raquo;</a></td>
			</tr>
			<tr height="30">
				<td>网站提交：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['apply'];?>
</b>　-　<a href="website.php?status=2">快速管理&raquo;</a></td>
				<td>意见反馈：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['feedback'];?>
</b>　-　<a href="feedback.php">快速管理&raquo;</a></td>
			</tr>
			<tr height="30">
				<td>分类统计：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['category'];?>
</b>　-　<a href="category.php">快速管理&raquo;</a></td>
				<td>站点内容：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['website'];?>
</b>　-　<a href="website.php">快速管理&raquo;</a></td>
			</tr>
			<tr height="30">
				<td>文章内容：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['article'];?>
</b>　-　<a href="article.php">快速管理&raquo;</a></td>
				<td>注册会员：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['user'];?>
</b>　-　<a href="user.php">快速管理&raquo;</a></td>
			</tr>
			<tr height="30">
				<td>自定义页面：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['page'];?>
</b>　-　<a href="page.php">快速管理&raquo;</a></td>
                <td>自定义标签：&nbsp;<b style="color: #008800;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['label'];?>
</b>　-　<a href="label.php">快速管理&raquo;</a></td>
			</tr>
		</table>
	</div>
                
	<h3 class="title"><em>服务器信息</em></h3>
    <div style="padding: 10px;">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr height="30">
				<td width="50%">服务器时间：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['datetime'];?>
</td>
				<td width="50%">服务器类型：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['software'];?>
</td>
			</tr>
			<tr height="30">
				<td>PHP版本：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['php_version'];?>
</td>
				<td>MySQL版本：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['mysql_version'];?>
</td>
			</tr>
			<tr height="30">
				<td>Smarty版本：<?php echo $_smarty_tpl->tpl_vars['server']->value['smarty_version'];?>
</td>
				<td>软件版本：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['soft_version'];?>
 - <a href="https://gitee.com/au5110/35dir" target="_blank">查看更新</a></td>
			</tr>
			<tr height="30">
            	<td>安全模式(safe_mode)：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['safemode'];?>
</td>
            	<td>全局变量(register_globals)：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['globals'];?>
</td>
			</tr>
			<tr height="30">
            	<td>伪静态(rewrite_module)：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['rewrite'];?>
 (只针对Apache有效)</td>
				<td>内存占用(memory_info)：&nbsp;<?php echo $_smarty_tpl->tpl_vars['server']->value['memory_info'];?>
</td>		
			</tr>
		</table>
</div>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
