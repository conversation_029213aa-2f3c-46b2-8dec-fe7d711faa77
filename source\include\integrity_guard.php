<?php
/*
 * <AUTHOR> 95DIR完整性保护系统
 * @Description  : 95DIR分类目录系统代码完整性保护模块
 * @Version      : 2.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 * 
 * 警告：此文件用于保护系统完整性，请勿修改！
 */

if (!defined('IN_HANFOX')) {
    exit('Access Denied');
}

/**
 * 完整性保护类
 */
class IntegrityGuard {
    
    private static $instance = null;
    private $file_hashes = array();
    private $protected_functions = array();
    private $runtime_checks = 0;
    
    private function __construct() {
        $this->init_file_hashes();
        $this->init_protected_functions();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 初始化文件哈希表
     */
    private function init_file_hashes() {
        // 这些哈希值应该在部署时动态生成
        $this->file_hashes = array(
            'source/init.php' => $this->calculate_file_hash('source/init.php'),
            'source/include/license.php' => $this->calculate_file_hash('source/include/license.php'),
            'source/include/security_core.php' => $this->calculate_file_hash('source/include/security_core.php'),
            'source/include/function.php' => $this->calculate_file_hash('source/include/function.php'),
            'index.php' => $this->calculate_file_hash('index.php')
        );
    }
    
    /**
     * 初始化受保护的函数列表
     */
    private function init_protected_functions() {
        $this->protected_functions = array(
            'verify_system_license',
            'advanced_security_check',
            'random_security_verify',
            'authcode',
            'quick_license_check'
        );
    }
    
    /**
     * 计算文件哈希（使用多重哈希）
     * @param string $file_path
     * @return string
     */
    private function calculate_file_hash($file_path) {
        $full_path = ROOT_PATH . $file_path;
        if (!file_exists($full_path)) {
            return '';
        }
        
        $content = file_get_contents($full_path);
        
        // 移除注释和空白行以减少误报
        $content = $this->normalize_code($content);
        
        // 多重哈希
        $hash1 = md5($content);
        $hash2 = sha1($content);
        $hash3 = crc32($content);
        
        return md5($hash1 . $hash2 . $hash3);
    }
    
    /**
     * 标准化代码（移除注释和多余空白）
     * @param string $code
     * @return string
     */
    private function normalize_code($code) {
        // 移除单行注释
        $code = preg_replace('/\/\/.*$/m', '', $code);
        
        // 移除多行注释
        $code = preg_replace('/\/\*.*?\*\//s', '', $code);
        
        // 移除多余的空白行
        $code = preg_replace('/\n\s*\n/', "\n", $code);
        
        // 移除行首行尾空白
        $code = preg_replace('/^\s+|\s+$/m', '', $code);
        
        return $code;
    }
    
    /**
     * 检查文件完整性
     * @return bool
     */
    public function check_file_integrity() {
        $this->runtime_checks++;
        
        // 限制检查频率（性能考虑）
        if ($this->runtime_checks > 3) {
            return true;
        }
        
        foreach ($this->file_hashes as $file => $expected_hash) {
            if (empty($expected_hash)) {
                continue;
            }
            
            $current_hash = $this->calculate_file_hash($file);
            
            // 允许一定的差异（考虑到注释修改等）
            if ($current_hash !== $expected_hash) {
                // 进行更严格的检查
                if (!$this->deep_integrity_check($file)) {
                    $this->log_integrity_violation($file, $expected_hash, $current_hash);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 深度完整性检查
     * @param string $file_path
     * @return bool
     */
    private function deep_integrity_check($file_path) {
        $full_path = ROOT_PATH . $file_path;
        if (!file_exists($full_path)) {
            return false;
        }
        
        $content = file_get_contents($full_path);
        
        // 检查关键代码片段是否存在
        $critical_patterns = array(
            'source/include/license.php' => array(
                'class LicenseValidator',
                'function verify_system_license',
                'decrypt_license_file'
            ),
            'source/include/security_core.php' => array(
                'class SecurityCore',
                'generate_dynamic_key',
                'comprehensive_security_check'
            ),
            'source/init.php' => array(
                'verify_system_license',
                'advanced_security_check'
            )
        );
        
        if (isset($critical_patterns[$file_path])) {
            foreach ($critical_patterns[$file_path] as $pattern) {
                if (strpos($content, $pattern) === false) {
                    return false;
                }
            }
        }
        
        // 检查是否有可疑的修改
        $suspicious_patterns = array(
            'return true;', // 可能的绕过代码
            'exit;',        // 可能的阻断代码
            '// hack',      // 明显的破解标记
            '/* bypass */', // 绕过标记
            'eval(',        // 动态执行
            'system(',      // 系统调用
            'exec(',        // 命令执行
            'shell_exec('   // Shell执行
        );
        
        $suspicious_count = 0;
        foreach ($suspicious_patterns as $pattern) {
            $suspicious_count += substr_count(strtolower($content), strtolower($pattern));
        }
        
        // 如果可疑模式过多，认为文件被篡改
        if ($suspicious_count > 5) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查函数完整性
     * @return bool
     */
    public function check_function_integrity() {
        foreach ($this->protected_functions as $func_name) {
            if (!function_exists($func_name)) {
                return false;
            }
            
            // 检查函数是否被重定义
            $reflection = new ReflectionFunction($func_name);
            $filename = $reflection->getFileName();
            
            // 验证函数来源文件
            if (!$this->is_trusted_file($filename)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否为可信文件
     * @param string $filename
     * @return bool
     */
    private function is_trusted_file($filename) {
        $trusted_paths = array(
            ROOT_PATH . 'source/include/',
            ROOT_PATH . 'source/',
            ROOT_PATH . 'system/'
        );
        
        foreach ($trusted_paths as $path) {
            if (strpos($filename, $path) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 记录完整性违规
     * @param string $file
     * @param string $expected_hash
     * @param string $current_hash
     */
    private function log_integrity_violation($file, $expected_hash, $current_hash) {
        $log_data = array(
            'timestamp' => time(),
            'file' => $file,
            'expected_hash' => $expected_hash,
            'current_hash' => $current_hash,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        );
        
        $log_file = ROOT_PATH . 'data/integrity_violations.log';
        $log_entry = date('Y-m-d H:i:s') . ' [INTEGRITY_VIOLATION] ' . json_encode($log_data) . "\n";
        
        @file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 运行时保护检查
     * @return bool
     */
    public function runtime_protection_check() {
        // 检查内存中的关键变量
        if (!isset($GLOBALS['DB'])) {
            return false;
        }
        
        // 检查关键常量
        $critical_constants = array('ROOT_PATH', 'APP_PATH', 'IN_HANFOX');
        foreach ($critical_constants as $const) {
            if (!defined($const)) {
                return false;
            }
        }
        
        // 检查执行环境
        if (php_sapi_name() === 'cli') {
            // 命令行环境可能是破解尝试
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取系统指纹
     * @return string
     */
    public function get_system_fingerprint() {
        $factors = array(
            $_SERVER['DOCUMENT_ROOT'] ?? '',
            $_SERVER['SERVER_SOFTWARE'] ?? '',
            PHP_VERSION,
            php_uname(),
            get_loaded_extensions(),
            ini_get_all()
        );
        
        return hash('sha256', serialize($factors));
    }
}

/**
 * 全局完整性检查函数
 * @return bool
 */
function integrity_guard_check() {
    static $checked = false;
    
    if ($checked) {
        return true;
    }
    
    $guard = IntegrityGuard::getInstance();
    
    // 执行完整性检查
    if (!$guard->check_file_integrity()) {
        return false;
    }
    
    // 执行函数完整性检查
    if (!$guard->check_function_integrity()) {
        return false;
    }
    
    // 执行运行时保护检查
    if (!$guard->runtime_protection_check()) {
        return false;
    }
    
    $checked = true;
    return true;
}

?>
