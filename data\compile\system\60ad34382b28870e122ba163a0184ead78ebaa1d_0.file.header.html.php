<?php
/* Smarty version 4.5.5, created on 2025-07-26 19:18:09
  from '/www/wwwroot/www.95dir.com/themes/system/header.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6884b9714fb600_00111709',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '60ad34382b28870e122ba163a0184ead78ebaa1d' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/header.html',
      1 => 1751610958,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6884b9714fb600_00111709 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/page.css" rel="stylesheet" type="text/css" />
<?php echo '<script'; ?>
 type="text/javascript" src="../public/scripts/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="../public/scripts/admin.js"><?php echo '</script'; ?>
>
<!--<?php echo '<script'; ?>
 type="text/javascript" src="../public/editor123/kindeditor-min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="../public/editor123/lang/zh_CN.js"><?php echo '</script'; ?>
>-->
<?php echo '<script'; ?>
 type="text/javascript" src="../public/editor/kindeditor-all-min.js?v=4.1.12"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="../public/editor/lang/zh_CN.js?v=4.1.12"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript">
$(document).ready(function(){
	$('.listbox tr').mouseover(function() {
		$(this).addClass('over');
	});
	
	$('.listbox tr').mouseout(function() {
		$(this).removeClass('over');
	});
});
<?php echo '</script'; ?>
>
</head>

<body>
<div id="wrapper"><?php }
}
