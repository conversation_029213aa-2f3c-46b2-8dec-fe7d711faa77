<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统授权验证模块
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

if (!defined('IN_HANFOX')) {
    exit('Access Denied');
}

/**
 * 授权验证主类 - 高级安全版本
 */
class LicenseValidator {

    private $license_file;
    private $error_message;
    private $security_core;
    private $verification_count = 0;

    public function __construct() {
        $this->license_file = ROOT_PATH . 'data/license.dat';
        $this->error_message = '';

        // 加载安全核心
        if (!class_exists('SecurityCore')) {
            require_once(APP_PATH . 'include/security_core.php');
        }
        $this->security_core = SecurityCore::getInstance();
    }
    
    /**
     * 主验证函数 - 简化版本
     * @return bool
     */
    public function validate() {
        $this->verification_count++;

        // 检查验证频率（防止暴力破解）
        if ($this->verification_count > 10) {
            $this->error_message = '验证次数过多，请稍后再试！';
            return false;
        }

        // 检查授权文件是否存在
        if (!file_exists($this->license_file)) {
            $this->error_message = '授权文件不存在，请联系开发者获取授权！';
            return false;
        }

        // 读取并解密授权文件
        $license_data = $this->decrypt_license_file();
        if (!$license_data) {
            $this->error_message = '授权文件损坏或无效，请重新获取授权！';
            return false;
        }

        // 验证域名
        if (!$this->verify_domain($license_data['domain'])) {
            $this->error_message = '域名授权验证失败，当前域名未获得授权！';
            return false;
        }

        // 验证到期时间
        if (!$this->verify_expire_time($license_data['expire_time'])) {
            $this->error_message = '授权已过期，请续费后继续使用！到期时间：' . date('Y-m-d H:i:s', $license_data['expire_time']);
            return false;
        }

        // 验证版本
        if (!$this->verify_version($license_data['version'])) {
            $this->error_message = '版本授权验证失败，当前版本未获得授权！';
            return false;
        }

        // 验证机器码（如果授权文件中包含机器码）
        if (!empty($license_data['machine_code'])) {
            if (!$this->verify_machine_code($license_data['machine_code'])) {
                $this->error_message = '机器码验证失败，当前服务器未获得授权！';
                return false;
            }
        }

        return true;
    }
    
    /**
     * 解密授权文件 - 高级安全版本
     * @return array|false
     */
    private function decrypt_license_file() {
        try {
            $file_content = file_get_contents($this->license_file);
            if (!$file_content) {
                return false;
            }

            $decrypted_content = false;

            // 使用原始密钥解密
            $original_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
            $decrypted_content = authcode($file_content, 'DECODE', $original_key);

            if (!$decrypted_content) {
                return false;
            }
            
            // 解析JSON数据
            $license_data = json_decode($decrypted_content, true);
            if (!$license_data || !is_array($license_data)) {
                return false;
            }
            
            // 验证必要字段
            $required_fields = ['domain', 'expire_time', 'version', 'license_type'];
            foreach ($required_fields as $field) {
                if (!isset($license_data[$field])) {
                    return false;
                }
            }

            // 验证签名（如果存在）
            if (isset($license_data['signature'])) {
                if (!$this->verify_signature($license_data)) {
                    return false;
                }
            }

            return $license_data;
            
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 验证签名
     * @param array $license_data
     * @return bool
     */
    private function verify_signature($license_data) {
        if (!isset($license_data['signature'])) {
            return false;
        }

        $signature = $license_data['signature'];
        $data_to_sign = $license_data;
        unset($data_to_sign['signature']);

        // 按键名排序
        ksort($data_to_sign);

        // 生成签名字符串
        $sign_string = '';
        foreach ($data_to_sign as $key => $value) {
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string = rtrim($sign_string, '&');

        // 计算签名
        $license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
        $calculated_signature = md5($sign_string . $license_key);

        return $signature === $calculated_signature;
    }

    /**
     * 验证域名
     * @param string $authorized_domain
     * @return bool
     */
    private function verify_domain($authorized_domain) {
        $current_domain = $this->get_current_domain();
        
        // 支持通配符域名
        if (strpos($authorized_domain, '*.') === 0) {
            $pattern = str_replace('*.', '', $authorized_domain);
            return strpos($current_domain, $pattern) !== false;
        }
        
        // 精确匹配
        return $current_domain === $authorized_domain;
    }
    
    /**
     * 获取当前域名
     * @return string
     */
    private function get_current_domain() {
        $domain = '';
        
        if (isset($_SERVER['HTTP_HOST'])) {
            $domain = $_SERVER['HTTP_HOST'];
        } elseif (isset($_SERVER['SERVER_NAME'])) {
            $domain = $_SERVER['SERVER_NAME'];
        }
        
        // 移除端口号
        $domain = preg_replace('/:\d+$/', '', $domain);
        
        // 转换为小写
        return strtolower($domain);
    }
    
    /**
     * 验证到期时间
     * @param int $expire_time
     * @return bool
     */
    private function verify_expire_time($expire_time) {
        return time() <= $expire_time;
    }
    
    /**
     * 验证版本
     * @param string $authorized_version
     * @return bool
     */
    private function verify_version($authorized_version) {
        if (!defined('SYS_VERSION')) {
            return false;
        }

        // 如果授权版本为 * 则表示支持所有版本
        if ($authorized_version === '*') {
            return true;
        }

        return SYS_VERSION === $authorized_version;
    }

    /**
     * 验证机器码
     * @param string $authorized_machine_code
     * @return bool
     */
    private function verify_machine_code($authorized_machine_code) {
        $current_machine_code = $this->generate_machine_code();
        return $current_machine_code === $authorized_machine_code;
    }

    /**
     * 验证高级校验和
     * @param array $license_data
     * @return bool
     */
    private function verify_advanced_checksum($license_data) {
        if (!isset($license_data['checksum'])) {
            return false;
        }

        $checksum = $license_data['checksum'];

        // 重新计算校验和
        $check_data = $license_data;
        unset($check_data['checksum'], $check_data['signature']);

        $check_string = $this->array_to_string($check_data);
        $calculated_checksum = hash('crc32', $check_string);

        return $checksum === $calculated_checksum;
    }

    /**
     * 验证高级数字签名
     * @param array $license_data
     * @return bool
     */
    private function verify_advanced_signature($license_data) {
        if (!isset($license_data['signature'])) {
            return false;
        }

        $signature = $license_data['signature'];

        // 重新计算签名
        $sign_data = $license_data;
        unset($sign_data['signature']);
        ksort($sign_data);

        $sign_string = $this->array_to_string($sign_data);
        $signing_key = $this->security_core->generate_dynamic_key('signature');

        $hash1 = hash('sha256', $sign_string);
        $hash2 = hash('md5', $hash1 . $signing_key);
        $hash3 = hash('sha1', $hash2 . $license_data['license_id']);

        $calculated_signature = strtoupper($hash3);

        return $signature === $calculated_signature;
    }

    /**
     * 数组转字符串（递归处理）
     * @param mixed $data
     * @return string
     */
    private function array_to_string($data) {
        if (is_array($data)) {
            $result = '';
            foreach ($data as $key => $value) {
                $result .= $key . '=' . $this->array_to_string($value) . '&';
            }
            return rtrim($result, '&');
        } else {
            return (string)$data;
        }
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public function get_error_message() {
        return $this->error_message;
    }
    
    /**
     * 生成机器码（用于后续的机器码验证）
     * @return string
     */
    public function generate_machine_code() {
        $factors = array();
        
        // CPU信息
        if (function_exists('shell_exec')) {
            $cpu_info = shell_exec('cat /proc/cpuinfo 2>/dev/null | grep "processor" | wc -l');
            if ($cpu_info) {
                $factors[] = trim($cpu_info);
            }
        }
        
        // 服务器软件信息
        if (isset($_SERVER['SERVER_SOFTWARE'])) {
            $factors[] = $_SERVER['SERVER_SOFTWARE'];
        }
        
        // PHP版本
        $factors[] = PHP_VERSION;
        
        // 服务器IP
        if (isset($_SERVER['SERVER_ADDR'])) {
            $factors[] = $_SERVER['SERVER_ADDR'];
        }
        
        // 文档根目录
        if (isset($_SERVER['DOCUMENT_ROOT'])) {
            $factors[] = $_SERVER['DOCUMENT_ROOT'];
        }
        
        // 生成机器码
        $machine_string = implode('|', $factors);
        return md5($machine_string);
    }
    
    /**
     * 远程验证授权
     * @return bool
     */
    public function remote_verify() {
        // 读取本地授权文件
        $license_data = $this->decrypt_license_file();
        if (!$license_data) {
            return false;
        }

        // 准备验证数据
        $verify_data = array(
            'domain' => $this->get_current_domain(),
            'machine_code' => $this->generate_machine_code(),
            'version' => defined('SYS_VERSION') ? SYS_VERSION : '',
            'license_signature' => $license_data['signature'] ?? '',
            'customer_email' => $license_data['customer_email'] ?? '',
            'timestamp' => time()
        );

        // 生成验证签名
        $verify_data['verify_signature'] = $this->generate_verify_signature($verify_data);

        // 发送验证请求（这里需要配置验证服务器地址）
        $verify_url = 'https://license.95dir.com/verify.php'; // 示例URL

        try {
            $response = $this->send_verify_request($verify_url, $verify_data);
            if ($response && isset($response['status']) && $response['status'] === 'valid') {
                return true;
            }
        } catch (Exception $e) {
            // 网络错误时，允许继续使用（宽松模式）
            return true;
        }

        return false;
    }

    /**
     * 生成验证签名
     * @param array $data
     * @return string
     */
    private function generate_verify_signature($data) {
        // 排除签名字段
        unset($data['verify_signature']);

        // 按键名排序
        ksort($data);

        // 生成签名字符串
        $sign_string = '';
        foreach ($data as $key => $value) {
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string = rtrim($sign_string, '&');

        // 生成签名
        return md5($sign_string . $this->license_key);
    }

    /**
     * 发送验证请求
     * @param string $url
     * @param array $data
     * @return array|false
     */
    private function send_verify_request($url, $data) {
        // 使用cURL发送请求
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code === 200 && $response) {
                return json_decode($response, true);
            }
        }

        // 如果cURL不可用，使用file_get_contents
        if (ini_get('allow_url_fopen')) {
            $context = stream_context_create(array(
                'http' => array(
                    'method' => 'POST',
                    'header' => 'Content-Type: application/x-www-form-urlencoded',
                    'content' => http_build_query($data),
                    'timeout' => 10
                )
            ));

            $response = @file_get_contents($url, false, $context);
            if ($response) {
                return json_decode($response, true);
            }
        }

        return false;
    }

    /**
     * 显示授权错误页面
     */
    public function show_license_error() {
        $error_msg = $this->get_error_message();
        
        header('Content-Type: text/html; charset=utf-8');
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>授权验证失败 - 95DIR分类目录系统</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .container { max-width: 600px; margin: 0 auto; background: #fff; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .error-icon { font-size: 48px; color: #e74c3c; margin-bottom: 20px; }
        .error-title { font-size: 20px; color: #e74c3c; margin-bottom: 20px; }
        .error-message { font-size: 16px; color: #666; line-height: 1.6; margin-bottom: 30px; padding: 20px; background: #ffeaea; border-left: 4px solid #e74c3c; }
        .contact-info { font-size: 14px; color: #999; text-align: center; }
        .machine-code { margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; }
        .machine-code strong { color: #333; }
        .machine-code code { background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR分类目录系统</div>
            <div class="error-icon">⚠</div>
            <div class="error-title">授权验证失败</div>
        </div>
        
        <div class="error-message">
            ' . htmlspecialchars($error_msg) . '
        </div>
        
        <div class="machine-code">
            <strong>当前域名：</strong> <code>' . htmlspecialchars($this->get_current_domain()) . '</code><br>
            <strong>机器码：</strong> <code>' . $this->generate_machine_code() . '</code><br>
            <strong>系统版本：</strong> <code>' . (defined('SYS_VERSION') ? SYS_VERSION : '未知') . '</code>
        </div>
        
        <div class="contact-info">
            请联系开发者获取授权文件<br>
            邮箱：<EMAIL><br>
            请提供上述域名和机器码信息
        </div>
    </div>
</body>
</html>';
        exit;
    }
}

/**
 * 全局授权验证函数
 * @return bool
 */
function verify_system_license() {
    static $license_checked = false;
    static $license_valid = false;
    
    // 避免重复检查
    if ($license_checked) {
        return $license_valid;
    }
    
    $validator = new LicenseValidator();
    $license_valid = $validator->validate();
    $license_checked = true;
    
    if (!$license_valid) {
        $validator->show_license_error();
    }
    
    return $license_valid;
}

/**
 * 快速授权检查（用于AJAX请求等）
 * @return bool
 */
function quick_license_check() {
    $validator = new LicenseValidator();
    return $validator->validate();
}

/**
 * 在线授权验证
 * @return bool
 */
function online_license_verify() {
    global $DB;

    // 检查上次验证时间
    $last_verify_time = 0;
    if (isset($DB)) {
        $table = $DB->table('options');
        $result = $DB->fetch_one("SELECT option_value FROM $table WHERE option_name='last_license_verify'");
        if ($result) {
            $last_verify_time = intval($result['option_value']);
        }
    }

    // 如果距离上次验证不到7天，跳过在线验证
    if (time() - $last_verify_time < 7 * 24 * 60 * 60) {
        return true;
    }

    // 执行在线验证
    $validator = new LicenseValidator();
    $result = $validator->remote_verify();

    // 更新验证时间
    if (isset($DB) && $result) {
        $table = $DB->table('options');
        $data = array('option_value' => time());
        $where = array('option_name' => 'last_license_verify');
        $idata = array('option_name' => 'last_license_verify', 'option_value' => time());

        $existing = $DB->fetch_one("SELECT option_name FROM $table WHERE option_name = 'last_license_verify'");
        if ($existing) {
            $DB->update($table, $data, $where);
        } else {
            $DB->insert($table, $idata);
        }
    }

    return $result;
}

/**
 * 文件完整性检查
 * @return bool
 */
function check_file_integrity() {
    $core_files = array(
        'source/init.php' => '',
        'source/include/function.php' => '',
        'source/include/license.php' => '',
        'system/common.php' => '',
        'index.php' => ''
    );

    // 获取预期的文件哈希值
    $expected_hashes = get_expected_file_hashes();

    foreach ($core_files as $file => $hash) {
        $file_path = ROOT_PATH . $file;
        if (!file_exists($file_path)) {
            return false;
        }

        $current_hash = md5_file($file_path);
        if (isset($expected_hashes[$file]) && $expected_hashes[$file] !== $current_hash) {
            // 文件被篡改
            return false;
        }
    }

    return true;
}

/**
 * 获取预期的文件哈希值
 * @return array
 */
function get_expected_file_hashes() {
    // 这里应该存储核心文件的MD5哈希值
    // 在实际部署时，这些值应该被加密存储
    return array(
        // 'source/init.php' => 'expected_md5_hash',
        // 'source/include/function.php' => 'expected_md5_hash',
        // 可以根据需要添加更多文件
    );
}

?>
