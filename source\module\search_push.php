<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 搜索引擎推送功能模块
 * 支持百度、谷歌、必应的网站地图和URL推送
 */

/**
 * 获取网站的所有URL列表
 */
function get_all_urls() {
    global $DB, $options;
    
    $urls = array();
    $site_url = rtrim($options['site_url'], '/');
    
    // 添加首页
    $urls[] = $site_url . '/';
    
    // 添加分类页面
    $categories = $DB->fetch_all("SELECT cate_id, cate_dir FROM " . $DB->table('categories') . " WHERE cate_status='1' ORDER BY cate_order ASC");
    foreach ($categories as $cate) {
        if ($cate['cate_dir']) {
            $urls[] = $site_url . '/' . $cate['cate_dir'] . '/';
        } else {
            $urls[] = $site_url . '/category.php?cid=' . $cate['cate_id'];
        }
    }
    
    // 添加网站详情页
    $websites = $DB->fetch_all("SELECT web_id, web_domain FROM " . $DB->table('websites') . " WHERE web_status='1' ORDER BY web_id DESC LIMIT 1000");
    foreach ($websites as $web) {
        $urls[] = $site_url . '/siteinfo.php?id=' . $web['web_id'];
    }
    
    // 添加文章页面
    $articles = $DB->fetch_all("SELECT art_id FROM " . $DB->table('articles') . " WHERE art_status='1' ORDER BY art_id DESC LIMIT 500");
    foreach ($articles as $art) {
        $urls[] = $site_url . '/article.php?id=' . $art['art_id'];
    }
    
    return $urls;
}

/**
 * 推送到百度搜索引擎
 */
function push_to_baidu($urls, $api_token = '') {
    if (empty($api_token)) {
        return array('success' => false, 'message' => '百度推送Token不能为空');
    }
    
    global $options;
    $site_url = parse_url($options['site_url'], PHP_URL_HOST);
    
    // 百度快速收录API
    $api_url = "http://data.zz.baidu.com/urls?site={$site_url}&token={$api_token}";
    
    $post_data = implode("\n", $urls);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: text/plain',
        'User-Agent: curl/7.12.1'
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $result = json_decode($response, true);
        if (isset($result['success'])) {
            return array(
                'success' => true, 
                'message' => "成功推送 {$result['success']} 个URL到百度",
                'data' => $result
            );
        } else {
            return array(
                'success' => false, 
                'message' => '百度推送失败：' . $response
            );
        }
    } else {
        return array(
            'success' => false, 
            'message' => "百度推送失败，HTTP状态码：{$http_code}"
        );
    }
}

/**
 * 推送到谷歌搜索引擎
 */
function push_to_google($sitemap_url, $api_key = '') {
    if (empty($api_key)) {
        return array('success' => false, 'message' => '谷歌API密钥不能为空');
    }
    
    // Google Search Console API
    $api_url = "https://www.googleapis.com/webmasters/v3/sites/" . urlencode($sitemap_url) . "/sitemaps/" . urlencode($sitemap_url . '/sitemap.xml');
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Authorization: Bearer ' . $api_key,
        'Content-Type: application/json'
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 || $http_code == 204) {
        return array('success' => true, 'message' => '成功提交网站地图到谷歌');
    } else {
        return array('success' => false, 'message' => "谷歌推送失败，HTTP状态码：{$http_code}，响应：{$response}");
    }
}

/**
 * 推送到必应搜索引擎
 */
function push_to_bing($urls, $api_key = '') {
    if (empty($api_key)) {
        return array('success' => false, 'message' => '必应API密钥不能为空');
    }
    
    global $options;
    $site_url = parse_url($options['site_url'], PHP_URL_HOST);
    
    // Bing Webmaster API
    $api_url = "https://ssl.bing.com/webmaster/api.svc/json/SubmitUrlbatch?apikey={$api_key}";
    
    $post_data = json_encode(array(
        'siteUrl' => $options['site_url'],
        'urlList' => array_slice($urls, 0, 10) // 必应每次最多提交10个URL
    ));
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'charset: utf-8'
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $result = json_decode($response, true);
        if (isset($result['d']) && $result['d'] === null) {
            return array('success' => true, 'message' => '成功推送URL到必应');
        } else {
            return array('success' => false, 'message' => '必应推送失败：' . $response);
        }
    } else {
        return array('success' => false, 'message' => "必应推送失败，HTTP状态码：{$http_code}");
    }
}

/**
 * 生成网站地图XML
 */
function generate_sitemap_xml() {
    global $options;
    
    $urls = get_all_urls();
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    foreach ($urls as $url) {
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url) . '</loc>' . "\n";
        $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        $xml .= '    <changefreq>daily</changefreq>' . "\n";
        $xml .= '    <priority>0.8</priority>' . "\n";
        $xml .= '  </url>' . "\n";
    }
    
    $xml .= '</urlset>';
    
    // 保存到网站根目录
    file_put_contents(ROOT_PATH . 'sitemap.xml', $xml);
    
    return $xml;
}

/**
 * 获取推送配置
 */
function get_push_config() {
    global $DB;
    
    $config = array();
    $table = $DB->table('options');
    
    $baidu_token = $DB->get_one("SELECT option_value FROM $table WHERE option_name='baidu_push_token'");
    $google_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='google_api_key'");
    $bing_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='bing_api_key'");
    
    $config['baidu_token'] = $baidu_token ? $baidu_token['option_value'] : '';
    $config['google_key'] = $google_key ? $google_key['option_value'] : '';
    $config['bing_key'] = $bing_key ? $bing_key['option_value'] : '';
    
    return $config;
}

/**
 * 保存推送配置
 */
function save_push_config($baidu_token, $google_key, $bing_key) {
    global $DB;
    
    $table = $DB->table('options');
    
    // 保存百度Token
    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('baidu_push_token', '$baidu_token')");
    
    // 保存谷歌API密钥
    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('google_api_key', '$google_key')");
    
    // 保存必应API密钥
    $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('bing_api_key', '$bing_key')");
    
    return true;
}

/**
 * 记录推送日志
 */
function log_push_result($engine, $result, $url_count = 0) {
    global $DB;
    
    $table = $DB->table('push_logs');
    
    // 创建日志表（如果不存在）
    $DB->query("CREATE TABLE IF NOT EXISTS $table (
        log_id int(11) NOT NULL AUTO_INCREMENT,
        engine varchar(20) NOT NULL,
        url_count int(11) NOT NULL DEFAULT 0,
        success tinyint(1) NOT NULL DEFAULT 0,
        message text,
        push_time int(11) NOT NULL,
        PRIMARY KEY (log_id),
        KEY engine (engine),
        KEY push_time (push_time)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8");
    
    $success = $result['success'] ? 1 : 0;
    $message = addslashes($result['message']);
    $push_time = time();
    
    $DB->query("INSERT INTO $table (engine, url_count, success, message, push_time) 
                VALUES ('$engine', '$url_count', '$success', '$message', '$push_time')");
}

/**
 * 获取推送日志
 */
function get_push_logs($limit = 50) {
    global $DB;
    
    $table = $DB->table('push_logs');
    
    $logs = $DB->fetch_all("SELECT * FROM $table ORDER BY log_id DESC LIMIT $limit");
    
    foreach ($logs as &$log) {
        $log['push_time_format'] = date('Y-m-d H:i:s', $log['push_time']);
        $log['success_text'] = $log['success'] ? '成功' : '失败';
    }
    
    return $logs;
}
?>
