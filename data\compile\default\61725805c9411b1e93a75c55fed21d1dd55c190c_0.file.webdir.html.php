<?php
/* Smarty version 4.5.5, created on 2025-07-30 15:24:29
  from '/www/wwwroot/www.95dir.com/themes/default/webdir.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889c8adeba041_92927098',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '61725805c9411b1e93a75c55fed21d1dd55c190c' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/webdir.html',
      1 => 1753568403,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889c8adeba041_92927098 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<?php if ($_smarty_tpl->tpl_vars['cate_id']->value > 0) {?>
<meta name="apple-mobile-web-app-title" content="<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站目录 - <?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
" />
<link rel="canonical" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" />
<?php } else { ?>
<meta name="apple-mobile-web-app-title" content="网站目录 - <?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
" />
<link rel="canonical" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir" />
<?php }?>
<meta name="theme-color" content="#6f42c1" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/css/logo-preview.css" rel="stylesheet" type="text/css" />

<!-- SEO优化 - 结构化数据：网站分类 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "<?php if ($_smarty_tpl->tpl_vars['cate_id']->value > 0) {
echo $_smarty_tpl->tpl_vars['cate_name']->value;
} else { ?>网站目录<?php }?>",
    "description": "<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
",
    "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir<?php if ($_smarty_tpl->tpl_vars['cate_id']->value > 0) {?>&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;
}?>",
    "mainEntity": {
        "@type": "ItemList",
        "name": "<?php if ($_smarty_tpl->tpl_vars['cate_id']->value > 0) {
echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站列表<?php } else { ?>网站目录列表<?php }?>",
        "numberOfItems": "<?php echo $_smarty_tpl->tpl_vars['total']->value;?>
",
        "itemListElement": [
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'web', false, NULL, 'weblist', array (
  'iteration' => true,
  'last' => true,
  'total' => true,
));
$_smarty_tpl->tpl_vars['web']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['web']->value) {
$_smarty_tpl->tpl_vars['web']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['iteration']++;
$_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['last'] = $_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['iteration'] === $_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['total'];
?>
            {
                "@type": "ListItem",
                "position": <?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['iteration'] : null);?>
,
                "item": {
                    "@type": "WebSite",
                    "name": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['web']->value['web_name'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
",
                    "url": "http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
",
                    "description": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['web']->value['web_intro'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
"
                }
            }<?php if (!(isset($_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['last']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_weblist']->value['last'] : null)) {?>,<?php }?>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        ]
    }
}
<?php echo '</script'; ?>
>

<!-- SEO优化 - 结构化数据：面包屑导航 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "首页",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "网站目录",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir"
        }
        <?php if ($_smarty_tpl->tpl_vars['cate_id']->value > 0) {?>
        ,{
            "@type": "ListItem",
            "position": 3,
            "name": "<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
"
        }
        <?php }?>
    ]
}
<?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/scripts/common.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/js/logo-optimizer.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="subcate" class="clearfix">
            	<h3><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
</h3>
                <ul class="scatelist">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['categories']->value, 'sub');
$_smarty_tpl->tpl_vars['sub']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['sub']->value) {
$_smarty_tpl->tpl_vars['sub']->do_else = false;
?>
                    <?php if ($_smarty_tpl->tpl_vars['sub']->value['cate_mod'] != 'article') {?>
                    <li><a href="<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_name'];?>
</a><em>(<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_postcount'];?>
)</em></li>
                    <?php }?>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
</h2>
            	<ul class="sitelist">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'w', false, NULL, 'list', array (
));
$_smarty_tpl->tpl_vars['w']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
网站logo - <?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
优质网站推荐" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
网站截图预览" class="thumb" loading="lazy" /></a><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['w']->value['is_today']) {?><span class="new-icon">new</span><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_ispay'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/audit.gif" alt="VIP认证网站" title="VIP认证网站" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_istop'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/top.gif" alt="置顶推荐网站" title="置顶推荐网站" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_isbest'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/best.gif" alt="精品推荐网站" title="精品推荐网站" border="0"><?php }?></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_intro'];?>
</p><address>
  <!-- 仅占位，无 <a> -->
  <span id="placeholder-<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
"
      class="link-placeholder"
      data-furl="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_furl'];?>
"     <!-- 跳转地址 -->
      data-text="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_url'];?>
"      <!-- 显示文字：一定要有 -->
      style="color:#666;">
      链接检测中…
</span>

  - <?php echo $_smarty_tpl->tpl_vars['w']->value['web_ctime'];?>
 -
  <a href="javascript:;" class="addfav"
     onClick="addfav(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)" title="点击收藏">收藏</a>
</address></div></li>
                	<?php
}
if ($_smarty_tpl->tpl_vars['w']->do_else) {
?>
                	<li>该目录下无任何内容！</li>
                	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</ul>
            	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
            </div>
        </div>
        <div id="mainbox-right">
            
            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,true), 'quick');
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站logo - VIP优质网站" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站截图预览" loading="lazy" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['quick']->value['is_today']) {?><span class="new-icon">new</span><?php }?></strong><p><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            
            <div class="blank10"></div>
            
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,5,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
网站logo - 推荐优质网站" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
网站截图预览" loading="lazy" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['best']->value['is_today']) {?><span class="new-icon">new</span><?php }?></strong><p><?php echo $_smarty_tpl->tpl_vars['best']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['best']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<!--<div class="ad250x250"><?php echo get_adcode(7);?>
</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,14), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
        </div>
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>
<?php echo '<script'; ?>
>
/* =========================================
   站点链接批量检测脚本
   ========================================= */

document.addEventListener('DOMContentLoaded', () => {

  /* ---- 配置区 ---- */
  const STATUS_TEXT = {
    200: '正常200',
    301: '跳转301',
    302: '跳转302',
    403: '禁止访问403',
    404: '未找到404',
    500: '服务器错误500',
    503: '服务不可用503'
  };
  const BLOCK_CODES     = [403, 404, 500, 503]; // 这些状态隐藏链接
  const CHECK_INTERVAL  = 300000;               // 5 分钟重检一次
  const CHECK_ENDPOINT  = '/module/status_check.php?url=';

  /* ---- 工具函数 ---- */
function makeLink(url, rawText, wid = '') {
  /* ① 显示文字：先去协议，再去尾部 “/” */
  const displayText = (rawText || url)
                        .replace(/^https?:\/\//i, '') // 去掉 http:// 或 https://
                        .replace(/\/$/, '');          // 如果最后还有 / 就去掉

  /* ② 生成链接 */
  const a = document.createElement('a');
  a.href        = url;          // href 仍保留协议
  a.target      = '_blank';
  a.className   = 'visit';
  a.textContent = displayText;  // 用户看到的文本
  if (wid && typeof clickout === 'function') {
    a.addEventListener('click', () => clickout(wid));
  }
  return a;
}

  async function fetchStatus(url) {
    try {
      const res = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));

      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();

      if (data.error) {
        console.error('状态检测API错误:', data.error, data.debug || '');
        throw new Error(data.error);
      }

      const code = Number(data.status);
      if (!code) {
        console.error('API返回数据异常:', data);
        throw new Error('接口返回缺少 status 字段');
      }

      return code;
    } catch (error) {
      console.error('链接状态检测失败:', {
        url: url,
        endpoint: CHECK_ENDPOINT + encodeURIComponent(url),
        error: error.message
      });
      throw error;
    }
  }

  /* ---- 检测：列表页 placeholder ---- */
  async function checkPlaceholder(ph) {
    const url = ph.dataset.furl;
    const txt = ph.dataset.text || url;               // 兜底：无 data-text 时用 URL
    const wid = ph.id.startsWith('placeholder-')
                ? ph.id.slice('placeholder-'.length)
                : '';

    // 初始提示
    ph.textContent = '链接检测中…';
    ph.style.color = '#666';

    try {
      const code = await fetchStatus(url);

      if (BLOCK_CODES.includes(code)) {
        // 403/404/500/503 &rarr; 红字提示，不显示链接
        ph.textContent = STATUS_TEXT[code] || `异常(${code})`;
        ph.style.color = '#f00';
      } else {
        // 正常 &rarr; 用 <a> 替换占位
        ph.replaceWith( makeLink(url, txt, wid) );
      }

    } catch (err) {
      // 网络 / JSON / CORS 等错误
      console.error('链接检测失败:', err);
      ph.textContent = '链接检测失败';
      ph.style.color = '#f00';
    }
  }

  /* ---- 检测：详情页单链 (#website-link) ---- */
  async function checkSingleLink() {
    const linkEl = document.getElementById('website-link');
    const phEl   = document.getElementById('website-link-placeholder');
    if (!linkEl || !phEl) return;   // 页面没有这对元素就跳过

    const url = linkEl.href;
    const txt = linkEl.textContent.trim();
    const wid = linkEl.id.startsWith('link-')
                ? linkEl.id.slice('link-'.length)
                : '';

    // 初始：隐藏链接、灰字提示
    linkEl.style.display    = 'none';
    phEl.textContent        = '链接检测中…';
    phEl.style.color        = '#666';
    phEl.style.display      = 'inline';

    try {
      const code = await fetchStatus(url);

      if (BLOCK_CODES.includes(code)) {
        phEl.textContent = STATUS_TEXT[code] || `异常(${code})`;
        phEl.style.color = '#f00';
      } else {
        // 正常 &rarr; 显示链接、隐藏占位
        linkEl.style.display = '';
        phEl.style.display   = 'none';
      }

    } catch (err) {
      console.error('链接检测失败:', err);
      phEl.textContent = '链接检测失败';
      phEl.style.color = '#f00';
    }
  }

  /* ---- 批量执行 ---- */
  function runBatch() {
    document.querySelectorAll('.link-placeholder').forEach(checkPlaceholder);
    checkSingleLink(); // 若有详情页单链同时处理
  }

  /* ---- 启动 ---- */
  runBatch();                       // 页面加载立即检测
  setInterval(runBatch, CHECK_INTERVAL);  // 定时重检
});
<?php echo '</script'; ?>
>

<!-- 建议的简单 CSS（放到你的公共样式里） -->
<style>
a.visit { color: #008000; }      /* 正常链接绿色 */
</style>


</body>
</html><?php }
}
