<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 支付处理函数模块
 * 用于快速提交的付费自动审核功能
 */

/**
 * 生成订单号
 * @return string 订单号
 */
function generate_order_no() {
    return date('Ymd') . substr(time(), -5) . substr(microtime(), 2, 5) . sprintf('%03d', rand(0, 999));
}

/**
 * 获取付费服务价格
 * @param int $payment_type 付费类型：1=VIP，2=推荐，3=快审
 * @return array|false 价格信息
 */
function get_payment_price($payment_type) {
    // 默认价格配置
    $default_prices = array(
        1 => array('price' => 50.00, 'name' => 'VIP收录', 'description' => 'VIP网站收录服务'),
        2 => array('price' => 20.00, 'name' => '推荐收录', 'description' => '推荐位网站收录服务'),
        3 => array('price' => 10.00, 'name' => '快审收录', 'description' => '快速审核收录服务')
    );
    
    if (!isset($default_prices[$payment_type])) {
        return false;
    }
    
    return $default_prices[$payment_type];
}

/**
 * 创建支付订单
 * @param array $form_data 表单数据
 * @param int $payment_type 付费类型
 * @return array|false 订单信息
 */
function create_payment_order($form_data, $payment_type) {
    global $DB;
    
    // 获取价格信息
    $price_info = get_payment_price($payment_type);
    if (!$price_info) {
        return false;
    }
    
    // 生成订单号
    $order_no = generate_order_no();
    
    // 订单过期时间（30分钟）
    $expire_time = time() + 1800;
    
    // 准备订单数据
    $order_data = array(
        'order_no' => $order_no,
        'payment_type' => $payment_type,
        'amount' => $price_info['price'],
        'status' => 0, // 待支付
        'web_name' => $form_data['web_name'],
        'web_url' => $form_data['web_url'],
        'web_tags' => $form_data['web_tags'],
        'web_intro' => $form_data['web_intro'],
        'web_owner' => $form_data['web_owner'],
        'web_email' => $form_data['web_email'],
        'cate_id' => $form_data['cate_id'],
        'expire_time' => $expire_time,
        'create_time' => time(),
        'update_time' => time()
    );
    
    // 插入订单
    $table = $DB->table('payment_orders');
    $DB->insert($table, $order_data);
    $order_id = $DB->insert_id();
    
    if ($order_id) {
        $order_data['order_id'] = $order_id;
        $order_data['price_info'] = $price_info;
        return $order_data;
    }
    
    return false;
}

/**
 * 调用支付接口创建支付订单
 * @param array $order_data 订单数据
 * @return array|false 支付接口返回结果
 */
function create_payment($order_data) {
    $api_url = 'https://api.rcku.cn/wxpay/pay';
    
    // 准备支付参数
    $params = array(
        'price' => $order_data['amount'],
        'orderid' => $order_data['order_no'],
        'description' => $order_data['price_info']['description']
    );
    
    // 构建请求URL
    $request_url = $api_url . '?' . http_build_query($params);
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 && $response) {
        $result = json_decode($response, true);
        if ($result && isset($result['ewm'])) {
            // 更新订单的二维码信息
            global $DB;
            $table = $DB->table('payment_orders');
            $update_data = array(
                'qr_code' => $result['ewm'],
                'update_time' => time()
            );
            $DB->update($table, $update_data, array('order_id' => $order_data['order_id']));
            
            return $result;
        }
    }
    
    return false;
}

/**
 * 查询支付结果
 * @param string $order_no 订单号
 * @return array 查询结果
 */
function check_payment_status($order_no) {
    $api_url = 'https://api.rcku.cn/wxpay/result';
    
    // 构建请求URL
    $request_url = $api_url . '?orderid=' . $order_no;
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $result = array(
        'success' => false,
        'paid' => false,
        'message' => '查询失败'
    );
    
    if ($http_code == 200 && $response) {
        $data = json_decode($response, true);
        if ($data) {
            $result['success'] = true;
            if (isset($data['status']) && $data['status'] == 1) {
                $result['paid'] = true;
                $result['message'] = '支付成功';
            } else {
                $result['message'] = '未支付';
            }
        }
    }
    
    return $result;
}

/**
 * 处理支付成功后的业务逻辑
 * @param string $order_no 订单号
 * @return bool 处理结果
 */
function process_payment_success($order_no) {
    global $DB;

    // 获取订单信息
    $order_table = $DB->table('payment_orders');
    $order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no' AND status = 0");

    if (!$order) {
        return false;
    }

    // 更新订单状态
    $update_data = array(
        'status' => 1, // 已支付
        'pay_time' => time(),
        'update_time' => time()
    );
    $DB->update($order_table, $update_data, array('order_id' => $order['order_id']));

    // 创建网站记录
    $web_data = array(
        'cate_id' => $order['cate_id'],
        'user_id' => 0, // 非会员用户ID为0
        'web_name' => $order['web_name'],
        'web_url' => $order['web_url'],
        'web_tags' => $order['web_tags'],
        'web_intro' => $order['web_intro'],
        'web_status' => 3, // 根据付费类型设置状态
        'web_ctime' => time()
    );

    // 根据付费类型设置网站属性
    switch ($order['payment_type']) {
        case 1: // VIP
            $web_data['web_ispay'] = 1;
            $web_data['web_vip_expire'] = time() + 365 * 24 * 3600; // 一年
            break;
        case 2: // 推荐
            $web_data['web_isbest'] = 1;
            $web_data['web_recommend_expire'] = time() + 30 * 24 * 3600; // 一个月
            break;
        case 3: // 快审
            $web_data['web_status'] = 3; // 直接审核通过
            break;
    }

    // 插入网站数据
    $websites_table = $DB->table('websites');
    $DB->insert($websites_table, $web_data);
    $web_id = $DB->insert_id();

    if ($web_id) {
        // 更新订单的web_id
        $DB->update($order_table, array('web_id' => $web_id), array('order_id' => $order['order_id']));

        // 插入统计数据
        $web_ip_numeric = sprintf("%u", ip2long(get_client_ip()));
        $stat_data = array(
            'web_id' => $web_id,
            'web_ip' => $web_ip_numeric,
            'web_grank' => 0,
            'web_brank' => 0,
            'web_srank' => 0,
            'web_arank' => 0,
            'web_utime' => time()
        );
        $DB->insert($DB->table('webdata'), $stat_data);

        // 更新分类统计
        $DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=".$order['cate_id']);

        // 记录付费记录
        $payment_data = array(
            'web_id' => $web_id,
            'web_name' => $order['web_name'],
            'web_url' => $order['web_url'],
            'payment_type' => $order['payment_type'],
            'payment_amount' => $order['amount'],
            'payment_time' => time(),
            'expire_time' => isset($web_data['web_vip_expire']) ? $web_data['web_vip_expire'] : (isset($web_data['web_recommend_expire']) ? $web_data['web_recommend_expire'] : 0),
            'operator' => 'system',
            'status' => 1,
            'remark' => '快速提交付费自动审核',
            'created_at' => time()
        );
        $DB->insert($DB->table('payment_records'), $payment_data);

        // 发送付费审核通过通知
        send_payment_approval_notifications($web_data, $order);

        // 发送通知（邮件、微信、钉钉）
        send_payment_success_notifications($web_data, $order);

        return true;
    }

    return false;
}

/**
 * 发送付费审核通过通知
 * @param array $web_data 网站数据
 * @param array $order 订单数据
 */
function send_payment_approval_notifications($web_data, $order) {
    global $options, $smarty, $DB;

    // 获取付费类型名称
    $payment_types = array(
        1 => array('name' => 'VIP收录', 'icon' => '👑', 'color' => '#6f42c1'),
        2 => array('name' => '推荐收录', 'icon' => '🔥', 'color' => '#dc3545'),
        3 => array('name' => '快审收录', 'icon' => '⚡', 'color' => '#ffc107')
    );

    $payment_info = isset($payment_types[$order['payment_type']]) ? $payment_types[$order['payment_type']] : array('name' => '付费收录', 'icon' => '💰', 'color' => '#007bff');

    // 1. 发送邮件通知用户（审核通过）
    send_user_approval_email($web_data, $order, $payment_info);

    // 2. 发送管理员通知（付费审核完成）
    send_admin_payment_notification($web_data, $order, $payment_info);

    // 3. 发送微信机器人通知
    send_wechat_payment_notification($web_data, $order, $payment_info);

    // 4. 发送钉钉机器人通知
    send_dingding_payment_notification($web_data, $order, $payment_info);
}

/**
 * 发送用户审核通过邮件
 */
function send_user_approval_email($web_data, $order, $payment_info) {
    global $options, $smarty;

    // 检查邮件配置
    if (empty($options['smtp_host']) || empty($options['smtp_user']) || empty($order['web_email'])) {
        return false;
    }

    try {
        require_once(APP_PATH.'include/sendmail.php');

        // 生成网站链接
        $site_link = $options['site_url'] . '?mod=siteinfo&web_id=' . $web_data['web_id'];

        // 设置模板变量
        $smarty->assign('site_name', $options['site_name']);
        $smarty->assign('site_url', $options['site_url']);
        $smarty->assign('web_name', $web_data['web_name']);
        $smarty->assign('web_url', $web_data['web_url']);
        $smarty->assign('site_link', $site_link);
        $smarty->assign('payment_type', $payment_info['name']);
        $smarty->assign('payment_amount', $order['amount']);
        $smarty->assign('payment_icon', $payment_info['icon']);

        // 生成邮件内容
        $subject = '[' . $options['site_name'] . '] ' . $payment_info['icon'] . ' ' . $payment_info['name'] . '审核通过！';

        $mailbody = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, ' . $payment_info['color'] . ' 0%, #667eea 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
                    <h1 style="margin: 0; font-size: 24px;">' . $payment_info['icon'] . ' ' . $payment_info['name'] . '审核通过！</h1>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">恭喜您的网站已成功收录</p>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: ' . $payment_info['color'] . ';">📋 收录信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; width: 100px;">网站名称：</td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">' . htmlspecialchars($web_data['web_name']) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold;">网站地址：</td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><a href="' . htmlspecialchars($web_data['web_url']) . '" target="_blank">' . htmlspecialchars($web_data['web_url']) . '</a></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold;">收录页面：</td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><a href="' . $site_link . '" target="_blank">点击查看收录页面</a></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold;">服务类型：</td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">' . $payment_info['icon'] . ' ' . $payment_info['name'] . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;">支付金额：</td>
                            <td style="padding: 8px 0; color: #e74c3c; font-weight: bold;">¥' . $order['amount'] . '</td>
                        </tr>
                    </table>
                </div>

                <div style="background: #e8f5e8; border-left: 4px solid #28a745; padding: 15px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0; color: #28a745;">✅ 审核结果</h4>
                    <p style="margin: 0;">您的网站已通过' . $payment_info['name'] . '审核，现已正式收录到我们的分类目录中！</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . $site_link . '" target="_blank" style="background: ' . $payment_info['color'] . '; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">🚀 查看收录页面</a>
                </div>

                <div style="border-top: 1px solid #dee2e6; padding-top: 20px; text-align: center; color: #6c757d; font-size: 14px;">
                    <p>感谢您选择 <a href="' . $options['site_url'] . '" target="_blank">' . $options['site_name'] . '</a></p>
                    <p style="margin: 5px 0 0 0;">此邮件由系统自动发送，请勿直接回复。</p>
                </div>
            </div>
        </body>
        </html>';

        return sendmail($order['web_email'], $subject, $mailbody);

    } catch (Exception $e) {
        error_log("付费审核通过邮件发送失败: " . $e->getMessage(), 3, ROOT_PATH . "data/payment_email_error.log");
        return false;
    }
}

/**
 * 发送管理员付费通知
 */
function send_admin_payment_notification($web_data, $order, $payment_info) {
    global $options;

    if (empty($options['smtp_host']) || empty($options['smtp_user']) || empty($options['admin_email'])) {
        return false;
    }

    try {
        require_once(APP_PATH.'include/sendmail.php');

        $subject = '[' . $options['site_name'] . '] ' . $payment_info['icon'] . ' ' . $payment_info['name'] . '自动审核完成';

        $mailbody = '
        <h2>' . $payment_info['icon'] . ' ' . $payment_info['name'] . '自动审核完成</h2>
        <p><strong>网站名称：</strong>' . htmlspecialchars($web_data['web_name']) . '</p>
        <p><strong>网站地址：</strong><a href="' . htmlspecialchars($web_data['web_url']) . '" target="_blank">' . htmlspecialchars($web_data['web_url']) . '</a></p>
        <p><strong>服务类型：</strong>' . $payment_info['name'] . '</p>
        <p><strong>支付金额：</strong>¥' . $order['amount'] . '</p>
        <p><strong>审核时间：</strong>' . date('Y-m-d H:i:s') . '</p>
        <p><strong>网站ID：</strong>' . $web_data['web_id'] . '</p>
        <p><a href="' . $options['site_url'] . 'system/" target="_blank">进入后台管理</a></p>
        ';

        return sendmail($options['admin_email'], $subject, $mailbody);

    } catch (Exception $e) {
        error_log("管理员付费通知邮件发送失败: " . $e->getMessage(), 3, ROOT_PATH . "data/payment_email_error.log");
        return false;
    }
}

/**
 * 发送微信机器人通知
 */
function send_wechat_payment_notification($web_data, $order, $payment_info) {
    global $options;

    if (empty($options['wechat_robot'])) {
        return false;
    }

    try {
        $messageData = [
            "msgtype" => "text",
            "text" => [
                "content" => $payment_info['icon'] . " " . $payment_info['name'] . "自动审核完成\n" .
                           "站点名称：" . $web_data['web_name'] . "\n" .
                           "站点地址：" . $web_data['web_url'] . "\n" .
                           "支付金额：¥" . $order['amount'] . "\n" .
                           "审核时间：" . date('Y-m-d H:i:s') . "\n" .
                           "网站ID：" . $web_data['web_id']
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $options['wechat_robot']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messageData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);

        $result = curl_exec($ch);
        curl_close($ch);

        return $result !== false;

    } catch (Exception $e) {
        error_log("微信机器人付费通知发送失败: " . $e->getMessage(), 3, ROOT_PATH . "data/payment_wechat_error.log");
        return false;
    }
}

/**
 * 发送钉钉机器人通知
 */
function send_dingding_payment_notification($web_data, $order, $payment_info) {
    global $options;

    if (empty($options['dingding_robot'])) {
        return false;
    }

    try {
        $messageData = [
            "msgtype" => "text",
            "text" => [
                "content" => $payment_info['icon'] . " " . $payment_info['name'] . "自动审核完成\n" .
                           "站点名称：" . $web_data['web_name'] . "\n" .
                           "站点地址：" . $web_data['web_url'] . "\n" .
                           "支付金额：¥" . $order['amount'] . "\n" .
                           "审核时间：" . date('Y-m-d H:i:s') . "\n" .
                           "网站ID：" . $web_data['web_id']
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $options['dingding_robot']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messageData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);

        $result = curl_exec($ch);
        curl_close($ch);

        return $result !== false;

    } catch (Exception $e) {
        error_log("钉钉机器人付费通知发送失败: " . $e->getMessage(), 3, ROOT_PATH . "data/payment_dingding_error.log");
        return false;
    }
}
?>
