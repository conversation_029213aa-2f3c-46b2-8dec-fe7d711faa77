<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 直接为test.95dir.com生成授权文件
 * @Version      : 1.0
 * @Date         : 2025-08-08
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

// 为test.95dir.com创建精确的授权信息
$license_info = array(
    'domain' => 'test.95dir.com',
    'expire_time' => strtotime('2025-12-31 23:59:59'), // 到2025年底
    'version' => '95DIR-v3.0', // 精确匹配版本
    'license_type' => 'developer',
    'customer_name' => '95DIR测试用户',
    'customer_email' => '<EMAIL>',
    'machine_code' => '7e217d6d470d761543b548bca7549699', // 精确匹配机器码
    'generate_time' => time(),
    'generator' => 'DirectTestLicenseCreator v1.0'
);

// 使用原始的授权密钥
$license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';

// 计算签名（使用与LicenseValidator相同的算法）
$sign_data = $license_info;
ksort($sign_data);
$sign_string = '';
foreach ($sign_data as $key => $value) {
    $sign_string .= $key . '=' . $value . '&';
}
$sign_string = rtrim($sign_string, '&');
$license_info['signature'] = md5($sign_string . $license_key);

// 转换为JSON
$json_data = json_encode($license_info, JSON_UNESCAPED_UNICODE);

// 使用authcode加密（与系统解密方式完全一致）
$encrypted_data = authcode($json_data, 'ENCODE', $license_key);

// 保存授权文件
$license_file = ROOT_PATH . 'data/license_test_95dir_com.dat';

// 确保目录存在
$data_dir = ROOT_PATH . 'data/';
if (!is_dir($data_dir)) {
    mkdir($data_dir, 0755, true);
}

// 保存文件
$result = file_put_contents($license_file, $encrypted_data);

// 验证生成的文件
$verify_result = false;
if ($result) {
    // 尝试解密验证
    $test_content = file_get_contents($license_file);
    $test_decrypt = authcode($test_content, 'DECODE', $license_key);
    $test_data = json_decode($test_decrypt, true);
    
    if ($test_data && isset($test_data['domain'])) {
        $verify_result = true;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>为test.95dir.com生成授权文件</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 700px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .result { padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; font-family: monospace; }
        .btn { background: #007bff; color: #fff; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-download { background: #17a2b8; }
        .btn-download:hover { background: #138496; }
        .steps { background: #fff3cd; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #ffeaa7; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 14px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">为test.95dir.com生成授权</div>
            <p>专门为您的测试站点创建正确的授权文件</p>
        </div>
        
        <?php if ($result !== false && $verify_result): ?>
        <div class="result success">
            <h3>✅ 授权文件生成并验证成功！</h3>
            <p>已为 <strong>test.95dir.com</strong> 生成专用授权文件</p>
            <p>文件大小：<?php echo strlen($encrypted_data); ?> 字节</p>
            <p>验证状态：✅ 解密验证通过</p>
        </div>
        <?php else: ?>
        <div class="result error">
            <h3>❌ 授权文件生成失败！</h3>
            <p>生成结果：<?php echo $result ? '文件保存成功' : '文件保存失败'; ?></p>
            <p>验证结果：<?php echo $verify_result ? '验证通过' : '验证失败'; ?></p>
        </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 授权详细信息</h3>
            <div class="info-item">
                <span class="info-label">授权域名：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['domain']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">绑定机器码：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['machine_code']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">到期时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['expire_time']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权版本：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['version']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">授权类型：</span>
                <span class="info-value"><?php echo htmlspecialchars($license_info['license_type']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">生成时间：</span>
                <span class="info-value"><?php echo date('Y-m-d H:i:s', $license_info['generate_time']); ?></span>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="?download=license_test_95dir_com.dat" class="btn btn-download">📥 下载授权文件 (license.dat)</a>
        </div>
        
        <div class="steps">
            <h4>🚀 立即部署步骤：</h4>
            <ol>
                <li><strong>下载文件</strong>：点击上面的下载按钮</li>
                <li><strong>上传到测试站</strong>：
                    <div class="code-block">test.95dir.com/data/license.dat</div>
                </li>
                <li><strong>设置权限</strong>：确保文件权限为 644</li>
                <li><strong>访问测试</strong>：
                    <div class="code-block">https://test.95dir.com</div>
                </li>
            </ol>
            
            <p><strong>⚠️ 重要提醒：</strong></p>
            <ul>
                <li>文件名必须是 <code>license.dat</code></li>
                <li>必须放在 <code>data</code> 目录下</li>
                <li>确保文件完整上传（<?php echo strlen($encrypted_data); ?> 字节）</li>
                <li>如果还是不行，请检查 data 目录权限</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="../index.php" class="btn">返回主站</a>
            <a href="license_test.php" class="btn btn-success">测试授权</a>
        </div>
    </div>
</body>
</html>

<?php
// 处理文件下载
if (isset($_GET['download'])) {
    $filename = basename($_GET['download']);
    $file_path = ROOT_PATH . 'data/' . $filename;
    
    if (file_exists($file_path)) {
        // 设置下载头
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="license.dat"');
        header('Content-Length: ' . filesize($file_path));
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        
        // 输出文件内容
        readfile($file_path);
        exit;
    } else {
        echo "文件不存在：" . htmlspecialchars($filename);
    }
}
?>
