{#include file="header.html"#}

<div class="main">
    <div class="title">
        <h2>{#$pagetitle#}</h2>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-box" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-around; text-align: center;">
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #007bff;">{#$stats.total#}</div>
                <div style="color: #666;">总评论数</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">{#$stats.normal#}</div>
                <div style="color: #666;">正常评论</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #dc3545;">{#$stats.hidden#}</div>
                <div style="color: #666;">已隐藏</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">{#$stats.violation#}</div>
                <div style="color: #666;">违规评论</div>
            </div>
        </div>
    </div>

    <!-- 搜索表单 -->
    <form method="get" action="{#$fileurl#}" class="search-form" style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px;">
        <input type="hidden" name="action" value="list">
        <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
            <input type="text" name="keywords" value="{#$keywords#}" placeholder="搜索评论内容或网站名称..." style="flex: 1; min-width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            
            <input type="number" name="web_id" value="{#if $web_id > 0#}{#$web_id#}{#/if#}" placeholder="网站ID" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            
            <select name="status" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="-1">全部状态</option>
                <option value="1" {#if $status == 1#}selected{#/if#}>正常</option>
                <option value="0" {#if $status == 0#}selected{#/if#}>已隐藏</option>
            </select>
            
            <button type="submit" style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">搜索</button>
            <a href="{#$fileurl#}?action=check_violation" onclick="return confirm('确定要检测所有评论的违规内容吗？')" style="background: #ffc107; color: #212529; padding: 8px 16px; border-radius: 4px; text-decoration: none;">检测违规</a>
        </div>
    </form>

    <!-- 批量操作 -->
    <form method="post" action="{#$fileurl#}" id="listform">
        <div class="toolbar" style="margin-bottom: 15px;">
            <input type="button" value="删除选中" onclick="if(confirm('确定要删除选中的评论吗？')) { document.getElementById('action').value='delete'; document.getElementById('listform').submit(); }" class="button" style="background: #dc3545; color: white;">
            <input type="button" value="恢复选中" onclick="if(confirm('确定要恢复选中的评论吗？')) { document.getElementById('action').value='restore'; document.getElementById('listform').submit(); }" class="button" style="background: #28a745; color: white;">
            <input type="hidden" name="action" id="action" value="">
        </div>

        <!-- 评论列表 -->
        <table class="list" cellpadding="0" cellspacing="0">
            <thead>
                <tr>
                    <th width="30"><input type="checkbox" onclick="checkall(this.form)"></th>
                    <th width="60">ID</th>
                    <th width="100">网站</th>
                    <th width="80">用户</th>
                    <th>评论内容</th>
                    <th width="80">评分</th>
                    <th width="60">状态</th>
                    <th width="120">时间</th>
                    <th width="100">操作</th>
                </tr>
            </thead>
            <tbody>
                {#foreach from=$comments item=comment#}
                <tr {#if $comment.status == 0#}style="background-color: #f8d7da;"{#/if#}>
                    <td><input type="checkbox" name="comment_id[]" value="{#$comment.comment_id#}"></td>
                    <td>{#$comment.comment_id#}</td>
                    <td>
                        {#if $comment.web_name#}
                            <a href="../?mod=siteinfo&web_id={#$comment.web_id#}" target="_blank" title="{#$comment.web_name#}">
                                {#$comment.web_name|truncate:10#}
                            </a>
                        {#else#}
                            <span style="color: #999;">已删除</span>
                        {#/if#}
                    </td>
                    <td>
                        <span {#if $comment.user_id > 0#}style="color: #007bff; font-weight: bold;"{#/if#}>
                            {#$comment.display_name#}
                        </span>
                    </td>
                    <td>
                        <div style="max-width: 300px;">
                            {#if $comment.is_violation#}
                                <span style="color: #dc3545; font-weight: bold;">[违规内容]</span>
                            {#/if#}
                            <div title="{#$comment.comment_content|escape#}">
                                {#$comment.short_content|escape#}
                            </div>
                            {#if $comment.parent_id > 0#}
                                <small style="color: #666;">↳ 回复评论 #{#$comment.parent_id#}</small>
                            {#/if#}
                        </div>
                    </td>
                    <td>
                        {#if $comment.parent_id == 0#}
                            <div style="font-size: 12px;">
                                <div>内容: {#$comment.content_quality#}★</div>
                                <div>服务: {#$comment.service_quality#}★</div>
                                <div>诚信: {#$comment.trust_level#}★</div>
                            </div>
                        {#else#}
                            <span style="color: #999;">-</span>
                        {#/if#}
                    </td>
                    <td>
                        {#if $comment.status == 1#}
                            <span style="color: #28a745;">正常</span>
                        {#else#}
                            <span style="color: #dc3545;">隐藏</span>
                        {#/if#}
                    </td>
                    <td style="font-size: 12px;">{#$comment.display_time#}</td>
                    <td>
                        {#if $comment.status == 1#}
                            <a href="{#$fileurl#}?action=delete&comment_id={#$comment.comment_id#}" 
                               onclick="return confirm('确定要删除这条评论吗？')" 
                               style="color: #dc3545; text-decoration: none; font-size: 12px;">删除</a>
                        {#else#}
                            <a href="{#$fileurl#}?action=restore&comment_id={#$comment.comment_id#}" 
                               onclick="return confirm('确定要恢复这条评论吗？')" 
                               style="color: #28a745; text-decoration: none; font-size: 12px;">恢复</a>
                        {#/if#}
                        |
                        <a href="../?mod=siteinfo&web_id={#$comment.web_id#}" target="_blank" 
                           style="color: #007bff; text-decoration: none; font-size: 12px;">查看</a>
                    </td>
                </tr>
                {#foreachelse#}
                <tr>
                    <td colspan="9" style="text-align: center; padding: 30px; color: #999;">
                        暂无评论数据
                    </td>
                </tr>
                {#/foreach#}
            </tbody>
        </table>
    </form>

    <!-- 分页 -->
    {#if $showpage#}
    <div class="pages" style="margin-top: 20px; text-align: center;">
        {#$showpage#}
    </div>
    {#/if#}
</div>

<style>
.stats-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-box div div:first-child {
    color: white !important;
}

.list {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border: 1px solid #ddd;
}

.list th {
    background: #f8f9fa;
    padding: 10px 8px;
    border: 1px solid #ddd;
    font-weight: bold;
    text-align: center;
}

.list td {
    padding: 8px;
    border: 1px solid #ddd;
    vertical-align: top;
}

.list tr:hover {
    background-color: #f5f5f5;
}

.button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
    font-size: 12px;
}

.toolbar {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.search-form input, .search-form select {
    font-size: 14px;
}

.search-form button {
    font-size: 14px;
    font-weight: bold;
}
</style>

<script>
function checkall(form) {
    var checkboxes = form.querySelectorAll('input[type="checkbox"][name="comment_id[]"]');
    var masterCheckbox = form.querySelector('input[type="checkbox"][onclick*="checkall"]');
    
    checkboxes.forEach(function(checkbox) {
        checkbox.checked = masterCheckbox.checked;
    });
}
</script>

{#include file="footer.html"#}
