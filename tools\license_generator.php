<?php
/*
 * <AUTHOR> 95DIR授权系统
 * @Description  : 95DIR分类目录系统授权文件生成工具
 * @Version      : 1.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载必要的函数
require(APP_PATH . 'include/function.php');

/**
 * 授权文件生成器类
 */
class LicenseGenerator {
    
    private $license_key;
    
    public function __construct() {
        $this->license_key = 'yeN3g9EbNfiaYfodV63dI1j8Fbk5HaL7W4yaW4y7u2j4Mf45mfg2v899g451k576';
    }
    
    /**
     * 生成授权文件
     * @param array $license_info
     * @return string|false
     */
    public function generate_license($license_info) {
        // 验证必要参数
        $required_fields = ['domain', 'expire_time', 'version', 'license_type'];
        foreach ($required_fields as $field) {
            if (!isset($license_info[$field]) || empty($license_info[$field])) {
                throw new Exception("缺少必要参数: {$field}");
            }
        }
        
        // 添加生成时间和其他信息
        $license_data = array(
            'domain' => $license_info['domain'],
            'expire_time' => $license_info['expire_time'],
            'version' => $license_info['version'],
            'license_type' => $license_info['license_type'],
            'customer_name' => $license_info['customer_name'] ?? '',
            'customer_email' => $license_info['customer_email'] ?? '',
            'machine_code' => $license_info['machine_code'] ?? '',
            'generate_time' => time(),
            'generator' => 'LicenseGenerator v1.0',
            'signature' => ''
        );
        
        // 生成签名
        $license_data['signature'] = $this->generate_signature($license_data);
        
        // 转换为JSON
        $json_data = json_encode($license_data, JSON_UNESCAPED_UNICODE);
        
        // 加密
        $encrypted_data = authcode($json_data, 'ENCODE', $this->license_key);
        
        return $encrypted_data;
    }
    
    /**
     * 生成数字签名
     * @param array $data
     * @return string
     */
    private function generate_signature($data) {
        // 排除signature字段
        unset($data['signature']);
        
        // 按键名排序
        ksort($data);
        
        // 生成签名字符串
        $sign_string = '';
        foreach ($data as $key => $value) {
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string = rtrim($sign_string, '&');
        
        // 生成签名
        return md5($sign_string . $this->license_key);
    }
    
    /**
     * 保存授权文件
     * @param string $encrypted_data
     * @param string $filename
     * @return bool
     */
    public function save_license_file($encrypted_data, $filename = null) {
        if (!$filename) {
            $filename = ROOT_PATH . 'data/license.dat';
        }
        
        // 确保目录存在
        $dir = dirname($filename);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        return file_put_contents($filename, $encrypted_data) !== false;
    }
    
    /**
     * 验证授权文件
     * @param string $filename
     * @return array|false
     */
    public function verify_license_file($filename) {
        if (!file_exists($filename)) {
            return false;
        }
        
        $encrypted_content = file_get_contents($filename);
        $decrypted_content = authcode($encrypted_content, 'DECODE', $this->license_key);
        
        if (!$decrypted_content) {
            return false;
        }
        
        $license_data = json_decode($decrypted_content, true);
        if (!$license_data) {
            return false;
        }
        
        // 验证签名
        $signature = $license_data['signature'];
        $calculated_signature = $this->generate_signature($license_data);
        
        if ($signature !== $calculated_signature) {
            return false;
        }
        
        return $license_data;
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $generator = new LicenseGenerator();
        
        // 获取表单数据
        $license_info = array(
            'domain' => trim($_POST['domain']),
            'expire_time' => strtotime($_POST['expire_date']),
            'version' => trim($_POST['version']),
            'license_type' => trim($_POST['license_type']),
            'customer_name' => trim($_POST['customer_name']),
            'customer_email' => trim($_POST['customer_email']),
            'machine_code' => trim($_POST['machine_code'])
        );
        
        // 生成授权文件
        $encrypted_data = $generator->generate_license($license_info);
        
        // 保存到文件
        $filename = ROOT_PATH . 'data/license_' . date('Ymd_His') . '.dat';
        $generator->save_license_file($encrypted_data, $filename);
        
        // 也保存为默认授权文件
        $generator->save_license_file($encrypted_data);
        
        $success_message = "授权文件生成成功！<br>文件保存位置：{$filename}";
        
    } catch (Exception $e) {
        $error_message = "生成失败：" . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR授权文件生成工具</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .form-group textarea { height: 80px; resize: vertical; }
        .btn { background: #007bff; color: #fff; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .help-text { font-size: 12px; color: #666; margin-top: 5px; }
        .row { display: flex; gap: 20px; }
        .col { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">95DIR授权文件生成工具</div>
            <p>用于生成95DIR分类目录系统的授权文件</p>
        </div>
        
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-error"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <form method="post">
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="domain">授权域名 *</label>
                        <input type="text" id="domain" name="domain" required placeholder="例如：www.example.com 或 *.example.com">
                        <div class="help-text">支持通配符域名，如 *.example.com</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="expire_date">到期时间 *</label>
                        <input type="datetime-local" id="expire_date" name="expire_date" required>
                        <div class="help-text">授权到期时间</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="version">授权版本 *</label>
                        <select id="version" name="version" required>
                            <option value="">请选择版本</option>
                            <option value="*">所有版本</option>
                            <option value="95DIR-v3.0">95DIR-v3.0</option>
                            <option value="95DIR-v2.0">95DIR-v2.0</option>
                        </select>
                        <div class="help-text">选择授权的系统版本</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="license_type">授权类型 *</label>
                        <select id="license_type" name="license_type" required>
                            <option value="">请选择类型</option>
                            <option value="commercial">商业授权</option>
                            <option value="personal">个人授权</option>
                            <option value="trial">试用授权</option>
                            <option value="developer">开发者授权</option>
                        </select>
                        <div class="help-text">授权使用类型</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="customer_name">客户姓名</label>
                        <input type="text" id="customer_name" name="customer_name" placeholder="客户姓名或公司名称">
                        <div class="help-text">可选，用于记录授权对象</div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="customer_email">客户邮箱</label>
                        <input type="email" id="customer_email" name="customer_email" placeholder="<EMAIL>">
                        <div class="help-text">可选，用于联系客户</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="machine_code">机器码</label>
                <input type="text" id="machine_code" name="machine_code" placeholder="客户提供的机器码">
                <div class="help-text">可选，用于绑定特定服务器（第二阶段功能）</div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">生成授权文件</button>
            </div>
        </form>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>使用说明：</h4>
            <ol>
                <li>填写客户的域名信息，支持通配符域名（如 *.example.com）</li>
                <li>设置授权到期时间</li>
                <li>选择对应的系统版本和授权类型</li>
                <li>生成的授权文件会保存在 data/ 目录下</li>
                <li>将 license.dat 文件发送给客户，放置在其网站的 data/ 目录下</li>
            </ol>
        </div>
    </div>
</body>
</html>
